# RAG and LLM Evaluation  

## 3.0 Content update explanation video


<a href="https://www.youtube.com/watch?v=qiWmCV0Knl0&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/qiWmCV0Knl0">
</a>


* 3.4 used to be "Ranking evaluation: vector search", but you will do it in homework
* Videos 3.5 - 3.8 used to be a part of module 4, but now they are a part of the evaluation module (module 4 focuses only on monitoring)
* The data files for retrieval evaluation are in the [search_evaluation](search_evaluation/) folder
* The data files for RAG evaluation are in the [rag_evaluation](rag_evaluation/) folder
  * We kept the old data files - the ones generated using this [old code](../cohorts/2024/04-monitoring/offline-rag-evaluation.ipynb)
  * In the [new notebook](rag_evaluation/offline-rag-evaluation.ipynb), you have minsearch instead of elasticsearch 
* Also, install [the sentence transformers](https://sbert.net/) library, we will use it for generating embeddings in some of the videos
  ```bash
  pip install sentence-transformers
  ```


## 3.1 Introduction

<a href="https://www.youtube.com/watch?v=APMrUnC_dy0&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/APMrUnC_dy0">
</a>

Plan for the section:

* Why do we need evaluation
* [Evaluation metrics](search_evaluation/evaluation-metrics.md)
* Ground truth / gold standard data
* Generating ground truth with LLM
* Evaluating the search resuls

**Note: in 2025 edition, we use Qdrant for performing vector search** (not Elastic Search).

For more details, see Module 2.


## 3.2 Getting ground truth data

<a href="https://www.youtube.com/watch?v=bpxi6fKcyLw&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/bpxi6fKcyLw">
</a>

* Approaches for getting evaluation data
* Using OpenAI to generate evaluation data

Links:

* [notebook](search_evaluation/ground-truth-data.ipynb)
* [documents with ids](search_evaluation/documents-with-ids.json)
* [queries generated by OpenAI (pickle)](search_evaluation/results.bin)
* [ground truth dataset](search_evaluation/ground-truth-data.csv)


## 3.3 Ranking evaluation: text search

<a href="https://www.youtube.com/watch?v=fdIV4xCsp0c&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/fdIV4xCsp0c">
</a>

* Elasticsearch with text results
* minsearch

Links:

* [Notebook](search_evaluation/evaluate-text.ipynb)


## 3.4 Evaluating Vector Search

That's [homework](../cohorts/2025/03-evaluation/homework.md)


## 3.5 Offline vs Online (RAG) evaluation

<a href="https://www.youtube.com/watch?v=yTKGSqkhgI4&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/yTKGSqkhgI4">
</a>

* Modules recap
* Online vs offline evaluation
* Offline evaluation metrics 


## 3.6 Generating data for offline RAG evaluation

<a href="https://www.youtube.com/watch?v=yTO5sRw6x78&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/yTO5sRw6x78">
</a>

**Note:** We talk about using ElasticSearch, but it's 
from 2024. Skip to 03:40.

When following the video, use the new code in the 
[notebook](rag_evaluation/offline-rag-evaluation.ipynb).

Links:

* [notebook](rag_evaluation/offline-rag-evaluation.ipynb)
* [results-gpt4o.csv](rag_evaluation/data/results-gpt4o.csv) (answers from GPT-4o)
* [results-gpt35.csv](rag_evaluation/data/results-gpt35.csv) (answers from GPT-3.5-Turbo)


## 3.7 Offline RAG evaluation: cosine similarity

<a href="https://www.youtube.com/watch?v=LlXclbD3pms&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/LlXclbD3pms">
</a>

Content

* A->Q->A' cosine similarity
* Evaluating gpt-4o
* Evaluating gpt-3.5-turbo
* Evaluating gpt-4o-mini

Links:

* [notebook](rag_evaluation/offline-rag-evaluation.ipynb)
* [results-gpt4o-cosine.csv](rag_evaluation/data/results-gpt4o-cosine.csv) (answers with cosine calculated from GPT-4o)
* [results-gpt35-cosine.csv](rag_evaluation/data/results-gpt35-cosine.csv) (answers with cosine calculated from GPT-3.5-Turbo)
* [results-gpt4o-mini.csv](rag_evaluation/data/results-gpt4o-mini.csv) (answers from GPT-4o-mini)
* [results-gpt4o-mini-cosine.csv](rag_evaluation/data/results-gpt4o-mini-cosine.csv) (answers with cosine calculated from GPT-4o-mini)


## 3.8 Offline RAG evaluation: LLM as a judge

<a href="https://www.youtube.com/watch?v=IB6jePK1s58&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/IB6jePK1s58">
</a>

* LLM as a judge
* A->Q->A' evaluation
* Q->A evaluation


Links:

* [notebook](rag_evaluation/offline-rag-evaluation.ipynb)
* [evaluations-aqa.csv](rag_evaluation/data/evaluations-aqa.csv) (A->Q->A evaluation results)
* [evaluations-qa.csv](rag_evaluation/data/evaluations-qa.csv) (Q->A evaluation results)



## Homework

See [here](../cohorts/2025/03-evaluation/homework.md)

# Notes
[Cohort 2025| Study notes and FAQ : LLM Evaluation ](https://github.com/niting9881/llm-zoomcamp/blob/main/03-evaluation/README.md)
* Did you take notes? Add them above this line (Send a PR with *links* to your notes)
