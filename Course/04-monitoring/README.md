# Module 4: Evaluation and Monitoring

In this module, we will learn how to evaluate and monitor our LLM and RAG system.

In the evaluation part, we assess the quality of our entire RAG
system before it goes live.

In the monitoring part, we collect, store and visualize
metrics to assess the answer quality of a deployed LLM. We also
collect chat history and user feedback.


* [Slides](https://docs.google.com/presentation/d/19a8Tsn6jXn1GqBSjTEhGusv4eX7fTyG5Qot1CvUeTOw/edit?usp=sharing)
* [code](code/)

## 4.1 Introduction



<a href="https://www.youtube.com/watch?v=nOjfqY9y4Vw&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/nOjfqY9y4Vw">
</a>


## 4.2 Instrumenting LLM Pipelines with OpenTelemetry

<a href="https://www.youtube.com/watch?v=_x72_txu46o&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/_x72_txu46o">
</a>



## 4.3 Evaluation of LLM Pipelines

<a href="https://www.youtube.com/watch?v=GmPXHjTa5ZA&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/GmPXHjTa5ZA">
</a>


# Links

* [Phoenix](https://github.com/Arize-ai/phoenix) (give it a star!)
* [Pre-Built Evals](https://arize.com/docs/phoenix/evaluation/how-to-evals/running-pre-tested-evals)
* [Retrieval (RAG) Relevance](https://arize.com/docs/phoenix/evaluation/how-to-evals/running-pre-tested-evals/retrieval-rag-relevance)
* [Phoenix Community Slack](https://join.slack.com/t/arize-ai/shared_invite/zt-26zg4u3lw-OjUNoLvKQ2Yv53EfvxW6Kg)


# Notes

* Notes from [2024 edition](../cohorts/2024/04-monitoring/)
* Did you take notes? Add them above this line (Send a PR with *links* to your notes)
