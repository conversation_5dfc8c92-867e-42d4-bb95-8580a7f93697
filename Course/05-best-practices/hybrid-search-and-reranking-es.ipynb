{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1f2faf02-2812-48cc-a446-20c51ce53db4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/python/3.10.13/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import json\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "from sentence_transformers import SentenceTransformer\n", "from elasticsearch import Elasticsearch"]}, {"cell_type": "code", "execution_count": 3, "id": "3f6cf20a-f641-4f58-8946-92171476c2e7", "metadata": {}, "outputs": [], "source": ["with open('documents-with-ids.json', 'rt') as f_in:\n", "    documents = json.load(f_in)"]}, {"cell_type": "code", "execution_count": 4, "id": "91fab490-d35b-47cc-94ce-d46194274457", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/python/3.10.13/lib/python3.10/site-packages/transformers/tokenization_utils_base.py:1601: FutureWarning: `clean_up_tokenization_spaces` was not set. It will be set to `True` by default. This behavior will be depracted in transformers v4.45, and will be then set to `False` by default. For more details check this issue: https://github.com/huggingface/transformers/issues/31884\n", "  warnings.warn(\n"]}], "source": ["model_name = 'multi-qa-MiniLM-L6-cos-v1'\n", "model = SentenceTransformer(model_name)"]}, {"cell_type": "code", "execution_count": 5, "id": "d63b5a43-0344-43e3-90af-48e0c105dd0e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████| 948/948 [01:55<00:00,  8.19it/s]\n"]}], "source": ["for doc in tqdm(documents):\n", "    question = doc['question']\n", "    text = doc['text']\n", "    qt = question + ' ' + text\n", "\n", "    doc['question_vector'] = model.encode(question)\n", "    doc['text_vector'] = model.encode(text)\n", "    doc['question_text_vector'] = model.encode(qt)"]}, {"cell_type": "code", "execution_count": 6, "id": "507be6a7-f937-4e41-81f3-d49e942ac3e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["ObjectApiResponse({'acknowledged': True, 'shards_acknowledged': True, 'index': 'course-questions'})"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["es_client = Elasticsearch('http://localhost:9200') \n", "\n", "index_settings = {\n", "    \"settings\": {\n", "        \"number_of_shards\": 1,\n", "        \"number_of_replicas\": 0\n", "    },\n", "    \"mappings\": {\n", "        \"properties\": {\n", "            \"text\": {\"type\": \"text\"},\n", "            \"section\": {\"type\": \"text\"},\n", "            \"question\": {\"type\": \"text\"},\n", "            \"course\": {\"type\": \"keyword\"},\n", "            \"id\": {\"type\": \"keyword\"},\n", "            \"question_vector\": {\n", "                \"type\": \"dense_vector\",\n", "                \"dims\": 384,\n", "                \"index\": True,\n", "                \"similarity\": \"cosine\"\n", "            },\n", "            \"text_vector\": {\n", "                \"type\": \"dense_vector\",\n", "                \"dims\": 384,\n", "                \"index\": True,\n", "                \"similarity\": \"cosine\"\n", "            },\n", "            \"question_text_vector\": {\n", "                \"type\": \"dense_vector\",\n", "                \"dims\": 384,\n", "                \"index\": True,\n", "                \"similarity\": \"cosine\"\n", "            },\n", "        }\n", "    }\n", "}\n", "\n", "index_name = \"course-questions\"\n", "\n", "es_client.indices.delete(index=index_name, ignore_unavailable=True)\n", "es_client.indices.create(index=index_name, body=index_settings)"]}, {"cell_type": "code", "execution_count": 7, "id": "b1e8ecc4-f726-4e9a-b648-0c9226743b6b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████| 948/948 [00:26<00:00, 36.07it/s]\n"]}], "source": ["for doc in tqdm(documents):\n", "    es_client.index(index=index_name, document=doc)"]}, {"cell_type": "markdown", "id": "73e7dcba-7ca8-4a69-9caa-e01571b8d76c", "metadata": {}, "source": ["## Hybrid search example"]}, {"cell_type": "code", "execution_count": 8, "id": "5e3a97cf-dc8a-4019-bf3a-ea29616c2065", "metadata": {}, "outputs": [], "source": ["course = \"data-engineering-zoomcamp\""]}, {"cell_type": "code", "execution_count": 9, "id": "7ef601f8-ffe0-4bdf-b3f6-e9dc765b2c52", "metadata": {}, "outputs": [], "source": ["query = 'I just discovered the course. Can I still join it?'"]}, {"cell_type": "code", "execution_count": 10, "id": "53dd190c-86d4-4d4a-a374-4075542094bc", "metadata": {}, "outputs": [], "source": ["v_q = model.encode(query)"]}, {"cell_type": "code", "execution_count": 35, "id": "d1547eb0-789a-43ee-a211-63719528e221", "metadata": {}, "outputs": [], "source": ["knn_query = {\n", "    \"field\": \"text_vector\",\n", "    \"query_vector\": v_q,\n", "    \"k\": 5,\n", "    \"num_candidates\": 10000,\n", "    \"boost\": 0.5,\n", "    \"filter\": {\n", "        \"term\": {\n", "            \"course\": course\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 36, "id": "c0ed651c-130c-40b5-bbe1-e0664440ddf9", "metadata": {}, "outputs": [], "source": ["keyword_query = {\n", "    \"bool\": {\n", "        \"must\": {\n", "            \"multi_match\": {\n", "                \"query\": query,\n", "                \"fields\": [\"question^3\", \"text\", \"section\"],\n", "                \"type\": \"best_fields\",\n", "                \"boost\": 0.5,\n", "            }\n", "        },\n", "        \"filter\": {\n", "            \"term\": {\n", "                \"course\": course\n", "            }\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 37, "id": "de4ee511-1ca9-4707-bf1e-ff0400871b7b", "metadata": {}, "outputs": [], "source": ["response = es_client.search(\n", "    index=index_name,\n", "    query=keyword_query,\n", "    knn=knn_query,\n", "    size=5\n", ")"]}, {"cell_type": "code", "execution_count": 38, "id": "3ba09df2-c55d-4f21-93ff-8de293d24dc5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'_index': 'course-questions',\n", "  '_id': 'ICPvR5EBO7bpWV6zkHVw',\n", "  '_score': 36.424633,\n", "  '_source': {'text': \"Yes, even if you don't register, you're still eligible to submit the homeworks.\\nBe aware, however, that there will be deadlines for turning in the final projects. So don't leave everything for the last minute.\",\n", "   'section': 'General course-related questions',\n", "   'question': 'Course - Can I still join the course after the start date?',\n", "   'course': 'data-engineering-zoomcamp',\n", "   'id': '7842b56a',\n", "   'question_vector': [0.0030358789954334497,\n", "    -0.0023871944285929203,\n", "    0.0358816422522068,\n", "    0.020998863503336906,\n", "    -0.018282365053892136,\n", "    0.06715094298124313,\n", "    -0.10277319699525833,\n", "    -0.11509548872709274,\n", "    -0.06606756150722504,\n", "    -0.004973309114575386,\n", "    -0.002861772431060672,\n", "    0.10543150454759598,\n", "    -0.0008143176091834903,\n", "    0.08418367058038712,\n", "    0.02704712562263012,\n", "    -0.031353775411844254,\n", "    -0.05154323950409889,\n", "    -0.049489956349134445,\n", "    0.053498510271310806,\n", "    0.004741442855447531,\n", "    -0.13610847294330597,\n", "    0.016541555523872375,\n", "    -0.07784715294837952,\n", "    0.06462235748767853,\n", "    0.03814757242798805,\n", "    -0.04093620181083679,\n", "    0.032365769147872925,\n", "    -0.017055032774806023,\n", "    0.050019729882478714,\n", "    -0.00375345628708601,\n", "    -0.04411797970533371,\n", "    0.0022928868420422077,\n", "    0.027998723089694977,\n", "    0.022567186504602432,\n", "    0.02095418982207775,\n", "    0.03941449150443077,\n", "    -0.0007631568587385118,\n", "    -0.0172516368329525,\n", "    -0.03559740632772446,\n", "    0.04144250229001045,\n", "    -0.01338113658130169,\n", "    -0.03866496682167053,\n", "    -0.015966536477208138,\n", "    0.03311391919851303,\n", "    0.041469138115644455,\n", "    0.04636891186237335,\n", "    0.010054372251033783,\n", "    -0.08450085669755936,\n", "    0.03752056136727333,\n", "    0.05225434899330139,\n", "    0.002637285739183426,\n", "    -0.0618680976331234,\n", "    0.011679016053676605,\n", "    -0.04379704222083092,\n", "    -0.0016357884742319584,\n", "    0.17986002564430237,\n", "    0.0023341814521700144,\n", "    -0.004212654661387205,\n", "    -0.08998120576143265,\n", "    0.07090847939252853,\n", "    -0.07258816063404083,\n", "    0.019129058346152306,\n", "    -0.07674622535705566,\n", "    -0.03295617550611496,\n", "    -0.014099235646426678,\n", "    -0.009027031250298023,\n", "    0.008977631106972694,\n", "    0.08070152997970581,\n", "    0.063536636531353,\n", "    0.028015535324811935,\n", "    0.02675623632967472,\n", "    0.04287584498524666,\n", "    0.014768087305128574,\n", "    0.045716542750597,\n", "    0.05623532459139824,\n", "    0.0804942175745964,\n", "    -0.021075265482068062,\n", "    0.025653501972556114,\n", "    0.08256993442773819,\n", "    -0.038129325956106186,\n", "    -0.05153496563434601,\n", "    0.038470495492219925,\n", "    0.02610129863023758,\n", "    -0.028379619121551514,\n", "    -0.04079042375087738,\n", "    -0.034507934004068375,\n", "    0.05899009108543396,\n", "    0.017292162403464317,\n", "    0.012982219457626343,\n", "    -0.02427682839334011,\n", "    0.02684706635773182,\n", "    -0.007099536247551441,\n", "    -0.002318795071914792,\n", "    0.0018779935780912638,\n", "    -0.04561064392328262,\n", "    0.04904411360621452,\n", "    -0.0033192718401551247,\n", "    0.019570522010326385,\n", "    0.07772140204906464,\n", "    0.013557561673223972,\n", "    -0.06471184641122818,\n", "    0.08406433463096619,\n", "    -0.032916367053985596,\n", "    0.0317685641348362,\n", "    0.006676312070339918,\n", "    -0.03568626567721367,\n", "    0.04975090175867081,\n", "    0.020977064967155457,\n", "    0.046528466045856476,\n", "    0.0017303683562204242,\n", "    0.0625462606549263,\n", "    0.01979629509150982,\n", "    0.06502961367368698,\n", "    -0.04385347664356232,\n", "    0.0010310091311112046,\n", "    0.09034036844968796,\n", "    -0.042172305285930634,\n", "    0.07182440161705017,\n", "    0.03044704906642437,\n", "    0.0027568491641432047,\n", "    0.030557531863451004,\n", "    0.06019622087478638,\n", "    0.06543651968240738,\n", "    -0.04194940626621246,\n", "    -0.05224351957440376,\n", "    -0.13805267214775085,\n", "    0.03443446010351181,\n", "    1.391160042418499e-30,\n", "    -0.033699020743370056,\n", "    -0.08616967499256134,\n", "    0.01331564411520958,\n", "    -0.04729745537042618,\n", "    0.031249187886714935,\n", "    -0.05709397420287132,\n", "    0.02903592959046364,\n", "    0.002098045079037547,\n", "    -0.05747297778725624,\n", "    0.010141773149371147,\n", "    -0.015067770145833492,\n", "    -0.033039726316928864,\n", "    0.018866876140236855,\n", "    -0.12119387090206146,\n", "    0.037736907601356506,\n", "    -0.038395803421735764,\n", "    0.006719027180224657,\n", "    -0.03813206031918526,\n", "    0.016285130754113197,\n", "    -0.004185942932963371,\n", "    0.02967025898396969,\n", "    -0.06790577620267868,\n", "    -0.030026813969016075,\n", "    -0.005851209629327059,\n", "    -0.007284811232239008,\n", "    0.037161290645599365,\n", "    0.017691558226943016,\n", "    0.008916049264371395,\n", "    0.003444650676101446,\n", "    0.005760367028415203,\n", "    0.050384532660245895,\n", "    0.00694583635777235,\n", "    -0.2038017362356186,\n", "    0.04625517129898071,\n", "    0.0006275299238041043,\n", "    0.006781439762562513,\n", "    0.035110943019390106,\n", "    0.03886368125677109,\n", "    0.08338703215122223,\n", "    -0.05187942087650299,\n", "    -0.00999826192855835,\n", "    -0.0290581826120615,\n", "    -0.004618439823389053,\n", "    -0.03841206431388855,\n", "    -0.02739207074046135,\n", "    -0.042614296078681946,\n", "    0.03893357515335083,\n", "    -0.02540450543165207,\n", "    0.030994845554232597,\n", "    0.0032377815805375576,\n", "    -0.05402807146310806,\n", "    -0.051378410309553146,\n", "    0.03874596580862999,\n", "    -0.07396597415208817,\n", "    -0.044319070875644684,\n", "    -0.022968612611293793,\n", "    0.02429545670747757,\n", "    0.00533262500539422,\n", "    -0.04109356924891472,\n", "    -0.09099765121936798,\n", "    0.03928957134485245,\n", "    0.019111784175038338,\n", "    -0.0664067417383194,\n", "    0.06304363161325455,\n", "    -0.10343020409345627,\n", "    0.028808485716581345,\n", "    0.010809928178787231,\n", "    -0.0861702561378479,\n", "    0.039909277111291885,\n", "    -0.09277823567390442,\n", "    -0.07120781391859055,\n", "    -0.02736351266503334,\n", "    -0.11500176787376404,\n", "    -0.01954318769276142,\n", "    -0.0026316174771636724,\n", "    0.043079376220703125,\n", "    0.008629639632999897,\n", "    0.015899045392870903,\n", "    0.09929502010345459,\n", "    -0.026113467290997505,\n", "    0.06020224466919899,\n", "    -0.027059631422162056,\n", "    -0.0458490289747715,\n", "    0.03151148557662964,\n", "    0.029637711122632027,\n", "    0.03479974716901779,\n", "    0.08134625107049942,\n", "    -0.025538260117173195,\n", "    0.06269923597574234,\n", "    0.017672738060355186,\n", "    -0.08673498034477234,\n", "    0.004906921647489071,\n", "    0.03746829554438591,\n", "    0.10807356983423233,\n", "    0.09329330176115036,\n", "    -1.6339962897025054e-33,\n", "    0.1438768357038498,\n", "    -0.02030225656926632,\n", "    0.027574054896831512,\n", "    -0.0339178666472435,\n", "    0.05497902259230614,\n", "    -0.031955841928720474,\n", "    0.044650930911302567,\n", "    0.045805007219314575,\n", "    -0.029797887429594994,\n", "    0.028350582346320152,\n", "    0.06769448518753052,\n", "    -0.041494518518447876,\n", "    0.008496949449181557,\n", "    -0.0009388770558871329,\n", "    0.01430158969014883,\n", "    -0.0494752861559391,\n", "    -0.01604420132935047,\n", "    0.015246182680130005,\n", "    -0.0431797094643116,\n", "    0.0122594079002738,\n", "    -0.08251841366291046,\n", "    -0.01287542749196291,\n", "    -0.008895794861018658,\n", "    -0.07542210072278976,\n", "    0.05499289184808731,\n", "    0.022808026522397995,\n", "    0.08616212010383606,\n", "    -0.003457692451775074,\n", "    -0.012436558492481709,\n", "    0.03046620450913906,\n", "    0.10336550325155258,\n", "    -0.06136125698685646,\n", "    -0.03536536172032356,\n", "    -0.059497084468603134,\n", "    -0.005761840380728245,\n", "    0.04134005680680275,\n", "    -0.028120195493102074,\n", "    -0.07863764464855194,\n", "    -0.046213630586862564,\n", "    0.07960967719554901,\n", "    0.014816176146268845,\n", "    -0.0068214754574000835,\n", "    0.010546742007136345,\n", "    -0.0031065631192177534,\n", "    0.0069765569642186165,\n", "    0.05870457738637924,\n", "    0.10561994463205338,\n", "    -0.027935268357396126,\n", "    -0.019868751987814903,\n", "    0.042926494032144547,\n", "    -0.050560787320137024,\n", "    0.03209524229168892,\n", "    0.018308883532881737,\n", "    -0.12651248276233673,\n", "    0.07742704451084137,\n", "    0.011259411461651325,\n", "    -0.028945304453372955,\n", "    -0.024034136906266212,\n", "    -0.0370866134762764,\n", "    -0.029373615980148315,\n", "    0.05436333641409874,\n", "    -0.025158971548080444,\n", "    0.020716072991490364,\n", "    -0.03519042581319809,\n", "    0.0852537527680397,\n", "    -0.03165107220411301,\n", "    -0.05412530153989792,\n", "    0.12868770956993103,\n", "    -0.03326082602143288,\n", "    0.004432697780430317,\n", "    -0.026434145867824554,\n", "    0.03529636561870575,\n", "    -0.030440667644143105,\n", "    -0.01753939688205719,\n", "    -0.013540316373109818,\n", "    -0.0862603709101677,\n", "    0.027311787009239197,\n", "    0.003940277732908726,\n", "    -0.003429661737754941,\n", "    -0.01535999309271574,\n", "    -0.08860872685909271,\n", "    0.06153767183423042,\n", "    -0.07527414709329605,\n", "    0.022762686014175415,\n", "    0.04615918919444084,\n", "    0.09381440281867981,\n", "    0.012213102541863918,\n", "    -0.010849429294466972,\n", "    0.01883467473089695,\n", "    -0.010083039291203022,\n", "    0.02265053801238537,\n", "    -0.03940627723932266,\n", "    0.10882851481437683,\n", "    0.023653876036405563,\n", "    -0.0691375806927681,\n", "    -6.61745352947773e-33,\n", "    -0.0493198037147522,\n", "    -0.0019405720522627234,\n", "    0.008752993308007717,\n", "    0.05457286909222603,\n", "    0.03494005650281906,\n", "    -0.019773047417402267,\n", "    -0.042059626430273056,\n", "    0.018659284338355064,\n", "    0.03138628229498863,\n", "    0.11269126087427139,\n", "    0.0031142833177000284,\n", "    0.07740269601345062,\n", "    0.051854196935892105,\n", "    -0.025963256135582924,\n", "    -0.027407964691519737,\n", "    0.026041341945528984,\n", "    0.055016953498125076,\n", "    -0.04038458690047264,\n", "    0.01797150820493698,\n", "    0.011853483505547047,\n", "    0.0058693652972579,\n", "    0.04921102151274681,\n", "    -0.04685528576374054,\n", "    0.013280190527439117,\n", "    -0.062083203345537186,\n", "    0.0275595560669899,\n", "    0.031357720494270325,\n", "    0.0020562063436955214,\n", "    -0.01707744598388672,\n", "    -0.0070129940286278725,\n", "    0.03179151937365532,\n", "    0.03670618683099747,\n", "    0.03714936226606369,\n", "    0.00979399960488081,\n", "    -0.038190409541130066,\n", "    -0.11714339256286621,\n", "    -0.01254135463386774,\n", "    -0.008553432300686836,\n", "    0.006335041951388121,\n", "    0.048173483461141586,\n", "    -0.0401337668299675,\n", "    0.007046960294246674,\n", "    0.11880563199520111,\n", "    0.11730796098709106,\n", "    0.01613052375614643,\n", "    0.061908841133117676,\n", "    -0.04807127267122269,\n", "    0.0005797932390123606,\n", "    0.006115443538874388,\n", "    -0.03488163650035858,\n", "    -0.06376070529222488,\n", "    0.04643417149782181,\n", "    -0.07774316519498825,\n", "    -0.08548127114772797,\n", "    0.04066047444939613,\n", "    0.060116223990917206,\n", "    0.04028015583753586,\n", "    -0.024994269013404846,\n", "    -0.05960742011666298,\n", "    -0.011192476376891136,\n", "    -0.06851030141115189,\n", "    -0.11372791230678558,\n", "    -0.005351467058062553,\n", "    -0.04430495947599411],\n", "   'text_vector': [-0.07091190665960312,\n", "    -0.0966142937541008,\n", "    -0.02184191904962063,\n", "    0.005265685264021158,\n", "    0.030283216387033463,\n", "    0.0037185216788202524,\n", "    -0.09556533396244049,\n", "    -0.007366143632680178,\n", "    -0.08307092636823654,\n", "    -0.027134675532579422,\n", "    -0.010136952623724937,\n", "    0.023012734949588776,\n", "    0.02019437588751316,\n", "    0.007188769988715649,\n", "    0.049683015793561935,\n", "    0.04341162368655205,\n", "    -0.0562698170542717,\n", "    -0.06492176651954651,\n", "    0.04255332797765732,\n", "    -0.021249230951070786,\n", "    -0.11276867985725403,\n", "    0.007847518660128117,\n", "    -0.024429524317383766,\n", "    -0.0019046778324991465,\n", "    0.06854445487260818,\n", "    0.049874115735292435,\n", "    0.0069945030845701694,\n", "    0.005410071462392807,\n", "    -0.01400085724890232,\n", "    0.06733166426420212,\n", "    -0.014997473917901516,\n", "    -0.033533670008182526,\n", "    0.022627627477049828,\n", "    -0.016331102699041367,\n", "    0.13751623034477234,\n", "    0.05978050082921982,\n", "    0.01858431287109852,\n", "    -0.013170692138373852,\n", "    0.030887233093380928,\n", "    -0.04455731436610222,\n", "    -0.007692230399698019,\n", "    -0.0169255081564188,\n", "    -0.06294991821050644,\n", "    0.024867799133062363,\n", "    -0.00699077732861042,\n", "    0.009911544620990753,\n", "    9.681258234195411e-05,\n", "    -0.08258810639381409,\n", "    -0.08262460678815842,\n", "    0.05747838318347931,\n", "    0.0518336184322834,\n", "    -0.03277237340807915,\n", "    0.013544444926083088,\n", "    -0.03739284351468086,\n", "    -0.008223694749176502,\n", "    0.055021122097969055,\n", "    0.09075308591127396,\n", "    0.014773468486964703,\n", "    -0.03558962419629097,\n", "    -0.014257997274398804,\n", "    -0.023607755079865456,\n", "    -0.04515087604522705,\n", "    -0.03157797083258629,\n", "    0.042979925870895386,\n", "    0.048418257385492325,\n", "    0.10764530301094055,\n", "    -0.050698213279247284,\n", "    -0.017731238156557083,\n", "    0.062369562685489655,\n", "    0.015722939744591713,\n", "    -0.07507255673408508,\n", "    0.009242046624422073,\n", "    0.062428027391433716,\n", "    0.07935915887355804,\n", "    -0.0015530148521065712,\n", "    -0.01947825588285923,\n", "    -0.01026492565870285,\n", "    0.06395792961120605,\n", "    0.06584541499614716,\n", "    0.0459834486246109,\n", "    -0.013537733815610409,\n", "    0.030606772750616074,\n", "    -0.010117605328559875,\n", "    -0.020966090261936188,\n", "    -0.04601258784532547,\n", "    -0.022890370339155197,\n", "    0.08187884092330933,\n", "    0.08200664073228836,\n", "    0.0626051053404808,\n", "    -0.04053056240081787,\n", "    0.07135585695505142,\n", "    -0.024112975224852562,\n", "    0.02013210952281952,\n", "    -0.02031654492020607,\n", "    -0.055114828050136566,\n", "    0.006477375980466604,\n", "    0.07309366017580032,\n", "    0.07591711729764938,\n", "    -0.01695440709590912,\n", "    -0.05295165628194809,\n", "    -0.049533504992723465,\n", "    -0.018158581107854843,\n", "    0.0071074930019676685,\n", "    -0.058213621377944946,\n", "    -0.02563844807446003,\n", "    0.02780936285853386,\n", "    -0.02554289810359478,\n", "    -0.06255444139242172,\n", "    -0.006390176247805357,\n", "    -0.041597217321395874,\n", "    0.01294037327170372,\n", "    -0.003735969541594386,\n", "    0.07793556153774261,\n", "    0.01597415842115879,\n", "    0.019239207729697227,\n", "    -0.00087457470363006,\n", "    -0.04853687062859535,\n", "    -0.022110590711236,\n", "    -0.020909292623400688,\n", "    0.023157287389039993,\n", "    0.06065639853477478,\n", "    0.12316159158945084,\n", "    0.07134721428155899,\n", "    -0.0617106594145298,\n", "    0.05128870904445648,\n", "    -0.11604715138673782,\n", "    0.04570207744836807,\n", "    7.406517660039583e-31,\n", "    -0.01242015976458788,\n", "    -0.05052646994590759,\n", "    0.006884296424686909,\n", "    -0.07502176612615585,\n", "    0.06051240861415863,\n", "    -0.06870178133249283,\n", "    0.1376631110906601,\n", "    0.06329380720853806,\n", "    0.02189561538398266,\n", "    -0.028774816542863846,\n", "    0.040225278586149216,\n", "    0.008694520220160484,\n", "    0.06633540987968445,\n", "    0.04650404304265976,\n", "    0.0007899394840933383,\n", "    0.02836628071963787,\n", "    0.028065349906682968,\n", "    -0.0526382178068161,\n", "    0.009210546500980854,\n", "    0.047143369913101196,\n", "    0.037254150956869125,\n", "    -0.030176496133208275,\n", "    0.005976375192403793,\n", "    -0.0010217217495664954,\n", "    0.021557332947850227,\n", "    0.05452688783407211,\n", "    0.007386220619082451,\n", "    0.023831574246287346,\n", "    -0.06094605103135109,\n", "    0.02994043380022049,\n", "    -0.02372194454073906,\n", "    -0.01955607533454895,\n", "    -0.03499647602438927,\n", "    -0.006245357915759087,\n", "    -0.016195887699723244,\n", "    0.047725219279527664,\n", "    -0.09388961642980576,\n", "    0.01108777616173029,\n", "    0.04081306234002113,\n", "    -0.015719301998615265,\n", "    0.019709214568138123,\n", "    0.0018760620150715113,\n", "    -0.055505260825157166,\n", "    -0.013064485974609852,\n", "    0.07079993933439255,\n", "    -0.05452272668480873,\n", "    0.10515543073415756,\n", "    -0.01894335076212883,\n", "    0.10626471042633057,\n", "    -0.014953948557376862,\n", "    0.02690710313618183,\n", "    -0.058037709444761276,\n", "    -0.04237772151827812,\n", "    -0.04518548399209976,\n", "    0.04449673742055893,\n", "    0.012399803847074509,\n", "    0.01607814058661461,\n", "    0.009141857735812664,\n", "    -0.03687158226966858,\n", "    -0.09439913183450699,\n", "    -0.0060831522569060326,\n", "    -0.03839525580406189,\n", "    -0.12177621573209763,\n", "    0.08159521967172623,\n", "    -0.05683586001396179,\n", "    0.020512690767645836,\n", "    -0.013009075075387955,\n", "    -0.02826913259923458,\n", "    0.0494314506649971,\n", "    -0.08301997184753418,\n", "    -0.0019643150735646486,\n", "    -0.08114413172006607,\n", "    -0.10107078403234482,\n", "    0.03962267190217972,\n", "    0.034594591706991196,\n", "    0.058598220348358154,\n", "    0.038910314440727234,\n", "    0.02013443224132061,\n", "    -0.07036706805229187,\n", "    -0.031590405851602554,\n", "    0.10598874092102051,\n", "    0.009453415870666504,\n", "    -0.026078930124640465,\n", "    -0.011808628216385841,\n", "    0.08040188252925873,\n", "    -0.01177340466529131,\n", "    0.11619935929775238,\n", "    0.058432213962078094,\n", "    -0.027450215071439743,\n", "    -0.008583685383200645,\n", "    0.021234169602394104,\n", "    -0.03810783848166466,\n", "    -0.07946737855672836,\n", "    0.058209165930747986,\n", "    -0.05282490327954292,\n", "    -4.192610721822898e-33,\n", "    -0.034219834953546524,\n", "    -0.028241906315088272,\n", "    -0.04732159897685051,\n", "    -0.018430456519126892,\n", "    -0.003721211338415742,\n", "    0.06166604533791542,\n", "    0.04330756142735481,\n", "    -0.0769520252943039,\n", "    -0.029796060174703598,\n", "    0.05989975482225418,\n", "    -0.0016081543872132897,\n", "    -0.023990578949451447,\n", "    -0.06131785362958908,\n", "    0.015505014918744564,\n", "    -0.046527039259672165,\n", "    -0.04676390439271927,\n", "    -0.07585813850164413,\n", "    0.042092930525541306,\n", "    0.0013105478137731552,\n", "    -0.0028015943244099617,\n", "    -0.07839088141918182,\n", "    -0.015338648110628128,\n", "    0.00028535674209706485,\n", "    -0.018389157950878143,\n", "    0.04479740932583809,\n", "    -0.03884204477071762,\n", "    -0.0475265309214592,\n", "    -0.046050455421209335,\n", "    -0.02170206606388092,\n", "    -0.006885293871164322,\n", "    0.033212609589099884,\n", "    -0.0743655264377594,\n", "    -0.05797980725765228,\n", "    -0.016799943521618843,\n", "    0.036671001464128494,\n", "    -0.04792896285653114,\n", "    0.0004663421423174441,\n", "    -0.056438595056533813,\n", "    -0.0331028513610363,\n", "    0.09929435700178146,\n", "    0.029067181050777435,\n", "    -0.029709134250879288,\n", "    -0.04071146622300148,\n", "    0.011238748207688332,\n", "    -0.06481156498193741,\n", "    0.01329710241407156,\n", "    0.03256268799304962,\n", "    0.09996244311332703,\n", "    0.00923181138932705,\n", "    0.006319055333733559,\n", "    0.0401945523917675,\n", "    0.04244287312030792,\n", "    0.07923196256160736,\n", "    -0.04994485527276993,\n", "    0.037580907344818115,\n", "    0.03612072020769119,\n", "    4.3560739868553355e-05,\n", "    -0.05807948857545853,\n", "    -0.0007577831274829805,\n", "    0.004217289388179779,\n", "    0.00941280648112297,\n", "    0.04242191091179848,\n", "    0.08733855187892914,\n", "    -0.049686651676893234,\n", "    0.06517872214317322,\n", "    -0.08124672621488571,\n", "    -0.032629892230033875,\n", "    0.07693536579608917,\n", "    -0.017817450687289238,\n", "    0.053528234362602234,\n", "    -0.013604945503175259,\n", "    -0.04739321395754814,\n", "    -0.10183849930763245,\n", "    -0.0385204553604126,\n", "    0.03444106504321098,\n", "    0.001340874470770359,\n", "    -0.05452222004532814,\n", "    0.024530066177248955,\n", "    -0.007862137630581856,\n", "    -0.12036743015050888,\n", "    -0.046526581048965454,\n", "    -0.004192620050162077,\n", "    -0.0016853965353220701,\n", "    -0.03107069618999958,\n", "    0.09263738989830017,\n", "    -0.009697227738797665,\n", "    0.012179969809949398,\n", "    -0.0376814603805542,\n", "    0.044366899877786636,\n", "    0.06018253043293953,\n", "    0.07215893268585205,\n", "    -0.003725287038832903,\n", "    0.10900899022817612,\n", "    -0.016888296231627464,\n", "    -0.028488190844655037,\n", "    -6.916335457801774e-33,\n", "    0.05668114870786667,\n", "    0.0817023366689682,\n", "    -0.03621397539973259,\n", "    -0.013258776627480984,\n", "    -0.030565086752176285,\n", "    -0.07181157916784286,\n", "    -0.05690303072333336,\n", "    0.027339935302734375,\n", "    0.04026823863387108,\n", "    0.15069448947906494,\n", "    0.08011243492364883,\n", "    -0.0643482580780983,\n", "    -0.0455503836274147,\n", "    0.023997539654374123,\n", "    -0.030308719724416733,\n", "    0.0036076412070542574,\n", "    0.11908786743879318,\n", "    -0.057235777378082275,\n", "    0.016896706074476242,\n", "    -0.04647023230791092,\n", "    -0.04877937212586403,\n", "    0.051499538123607635,\n", "    -0.1755664199590683,\n", "    -0.011838464066386223,\n", "    0.04615643620491028,\n", "    -0.027356864884495735,\n", "    0.06009575352072716,\n", "    0.057330597192049026,\n", "    -0.029298173263669014,\n", "    0.012215052731335163,\n", "    -0.018207378685474396,\n", "    -0.010700132697820663,\n", "    0.04255813732743263,\n", "    -0.05049838125705719,\n", "    0.03236440196633339,\n", "    -0.07739268988370895,\n", "    0.0525171160697937,\n", "    0.00419696094468236,\n", "    -0.01778440922498703,\n", "    0.0178633164614439,\n", "    -0.03879614919424057,\n", "    0.04863667115569115,\n", "    0.08452685177326202,\n", "    0.06312916427850723,\n", "    -0.06160537153482437,\n", "    -0.0158917885273695,\n", "    -0.03567688539624214,\n", "    -0.08871879428625107,\n", "    -0.00023221049923449755,\n", "    -0.07575000822544098,\n", "    -0.015369968488812447,\n", "    -0.02519393153488636,\n", "    0.011188570410013199,\n", "    -0.08532720059156418,\n", "    0.0876723900437355,\n", "    0.0765327662229538,\n", "    0.04917385056614876,\n", "    0.021243629977107048,\n", "    0.0122107844799757,\n", "    0.011596055701375008,\n", "    -0.013807734474539757,\n", "    0.03172734007239342,\n", "    0.04262305796146393,\n", "    -0.07548629492521286],\n", "   'question_text_vector': [-0.07135359942913055,\n", "    -0.05488087236881256,\n", "    0.010315806604921818,\n", "    0.02514219842851162,\n", "    0.01752229779958725,\n", "    0.03733212500810623,\n", "    -0.13700799643993378,\n", "    -0.060618024319410324,\n", "    -0.07796286046504974,\n", "    -0.010055039077997208,\n", "    -0.0058818417601287365,\n", "    0.057447850704193115,\n", "    0.0191632267087698,\n", "    0.05778709053993225,\n", "    0.035452503710985184,\n", "    0.026060547679662704,\n", "    -0.08295103162527084,\n", "    -0.07872345298528671,\n", "    0.052316680550575256,\n", "    -0.015712684020400047,\n", "    -0.13347111642360687,\n", "    0.019228029996156693,\n", "    -0.04688539728522301,\n", "    0.0309688001871109,\n", "    0.09005609154701233,\n", "    0.02511347271502018,\n", "    0.017883937805891037,\n", "    -0.013566125184297562,\n", "    0.014688157476484776,\n", "    0.029021019116044044,\n", "    -0.04580527916550636,\n", "    -0.014286261051893234,\n", "    0.030002297833561897,\n", "    -0.009298897348344326,\n", "    0.08802871406078339,\n", "    0.05368754640221596,\n", "    0.0165945366024971,\n", "    -0.03843110054731369,\n", "    -0.0095269326120615,\n", "    -0.0013269833289086819,\n", "    -0.004476139787584543,\n", "    -0.02259078435599804,\n", "    -0.03222767636179924,\n", "    0.03198651596903801,\n", "    0.027800697833299637,\n", "    0.032438382506370544,\n", "    0.025677883997559547,\n", "    -0.11732333153486252,\n", "    -0.001776561257429421,\n", "    0.0593525767326355,\n", "    0.021986857056617737,\n", "    -0.03951786831021309,\n", "    -0.02327122539281845,\n", "    -0.024381767958402634,\n", "    -0.008899649605154991,\n", "    0.11872143298387527,\n", "    0.039946943521499634,\n", "    0.034614987671375275,\n", "    -0.07183924317359924,\n", "    0.011869433335959911,\n", "    -0.03289301320910454,\n", "    -0.016178401187062263,\n", "    -0.05864695832133293,\n", "    0.017695415765047073,\n", "    0.012516742572188377,\n", "    0.06275277584791183,\n", "    -0.014007526449859142,\n", "    0.07030455768108368,\n", "    0.06174113601446152,\n", "    0.05820844694972038,\n", "    -0.02277413383126259,\n", "    0.05358588322997093,\n", "    0.05763183906674385,\n", "    0.05508321523666382,\n", "    0.03975974768400192,\n", "    0.033459994941949844,\n", "    -0.022455666214227676,\n", "    0.026245642453432083,\n", "    0.08848613500595093,\n", "    -0.0038315015845000744,\n", "    -0.047059666365385056,\n", "    0.05428428575396538,\n", "    0.005226286593824625,\n", "    -0.006290930323302746,\n", "    -0.028226377442479134,\n", "    -0.030359387397766113,\n", "    0.09107381850481033,\n", "    0.050694920122623444,\n", "    0.027036597952246666,\n", "    -0.059977978467941284,\n", "    0.04704459756612778,\n", "    -0.008300159126520157,\n", "    -0.03730534389615059,\n", "    0.010783007368445396,\n", "    -0.05189565196633339,\n", "    0.019376063719391823,\n", "    0.036998797208070755,\n", "    0.05698554590344429,\n", "    0.06218508630990982,\n", "    -0.03183141350746155,\n", "    -0.05689842253923416,\n", "    0.02752257138490677,\n", "    -0.024101998656988144,\n", "    -0.007970952428877354,\n", "    -0.002279674867168069,\n", "    -0.008260204456746578,\n", "    0.011484578251838684,\n", "    -0.0008405910339206457,\n", "    0.029393725097179413,\n", "    -0.025377700105309486,\n", "    0.047455813735723495,\n", "    0.0026449633296579123,\n", "    0.07729431986808777,\n", "    -0.027880793437361717,\n", "    0.01032080128788948,\n", "    0.06257063895463943,\n", "    -0.05465110018849373,\n", "    0.044231098145246506,\n", "    -0.003730516182258725,\n", "    0.0305488184094429,\n", "    0.05471181869506836,\n", "    0.11084823310375214,\n", "    0.07265115529298782,\n", "    -0.044478170573711395,\n", "    0.006846020929515362,\n", "    -0.1351717710494995,\n", "    0.06405895203351974,\n", "    9.35111962689302e-31,\n", "    -0.028276028111577034,\n", "    -0.06933073699474335,\n", "    0.019550450146198273,\n", "    -0.07070112973451614,\n", "    0.053902506828308105,\n", "    -0.08874203264713287,\n", "    0.101214699447155,\n", "    0.06865581125020981,\n", "    -0.03595485910773277,\n", "    0.008248956874012947,\n", "    0.024188674986362457,\n", "    -0.016441306099295616,\n", "    0.04687030240893364,\n", "    -0.047359809279441833,\n", "    0.007868140935897827,\n", "    -0.041966959834098816,\n", "    0.01934797503054142,\n", "    -0.10215481370687485,\n", "    -5.276560841593891e-07,\n", "    0.00409325584769249,\n", "    0.05615462735295296,\n", "    -0.024507904425263405,\n", "    -0.010303365997970104,\n", "    -0.013295861892402172,\n", "    0.001275151502341032,\n", "    0.05431507155299187,\n", "    0.013273118063807487,\n", "    0.036742981523275375,\n", "    -0.04202834144234657,\n", "    0.030180076137185097,\n", "    0.036933574825525284,\n", "    -0.009763795882463455,\n", "    -0.15951113402843475,\n", "    0.03605654090642929,\n", "    -0.041386283934116364,\n", "    0.01313572283834219,\n", "    -0.04758387431502342,\n", "    0.018177077174186707,\n", "    0.08521643280982971,\n", "    -0.05694711208343506,\n", "    0.011417010799050331,\n", "    -0.020177673548460007,\n", "    -0.04287893697619438,\n", "    -0.03228679299354553,\n", "    0.03889387473464012,\n", "    -0.034281641244888306,\n", "    0.07313317060470581,\n", "    -0.02120579592883587,\n", "    0.09769172966480255,\n", "    0.011695665307343006,\n", "    -0.028397956863045692,\n", "    -0.06918465346097946,\n", "    -0.0011756347957998514,\n", "    -0.0560181550681591,\n", "    0.002539340639486909,\n", "    -0.010323885828256607,\n", "    0.008280155248939991,\n", "    0.015013232827186584,\n", "    -0.07931894063949585,\n", "    -0.09508424997329712,\n", "    0.03064541704952717,\n", "    -0.02985788881778717,\n", "    -0.10973425954580307,\n", "    0.06318076699972153,\n", "    -0.09282249957323074,\n", "    0.023850884288549423,\n", "    0.0054724859073758125,\n", "    -0.06792953610420227,\n", "    0.050750818103551865,\n", "    -0.09885113686323166,\n", "    -0.06379874050617218,\n", "    -0.07743261009454727,\n", "    -0.11664839088916779,\n", "    0.002811217214912176,\n", "    0.003916364163160324,\n", "    0.06354612857103348,\n", "    0.025345129892230034,\n", "    -0.002971734618768096,\n", "    0.024876100942492485,\n", "    -0.033789876848459244,\n", "    0.07468825578689575,\n", "    -0.0033010407350957394,\n", "    -0.049106892198324203,\n", "    0.0017340099439024925,\n", "    0.03523217886686325,\n", "    0.019111670553684235,\n", "    0.10930934548377991,\n", "    0.029519561678171158,\n", "    0.022984381765127182,\n", "    0.006451821886003017,\n", "    -0.019329499453306198,\n", "    -0.0020278391893953085,\n", "    -0.008834865875542164,\n", "    0.09807292371988297,\n", "    0.0325816385447979,\n", "    -3.88117598320001e-33,\n", "    0.09587255865335464,\n", "    -0.01353421900421381,\n", "    -0.015629932284355164,\n", "    -0.037104178220033646,\n", "    0.034137312322854996,\n", "    0.023853681981563568,\n", "    0.03318202868103981,\n", "    -0.03090018965303898,\n", "    -0.030902400612831116,\n", "    0.061656076461076736,\n", "    0.046057701110839844,\n", "    -0.028023382648825645,\n", "    -0.02423020638525486,\n", "    -0.0018582013435661793,\n", "    -0.02745630592107773,\n", "    -0.06403091549873352,\n", "    -0.06063302606344223,\n", "    0.03122083656489849,\n", "    -0.021142035722732544,\n", "    -0.008768299594521523,\n", "    -0.09642701596021652,\n", "    -0.0337529182434082,\n", "    -0.006186670158058405,\n", "    -0.07579708844423294,\n", "    0.06817294657230377,\n", "    -0.014360339380800724,\n", "    0.014753852039575577,\n", "    -0.013360241428017616,\n", "    -0.0013600136153399944,\n", "    -0.00026679763686843216,\n", "    0.08105045557022095,\n", "    -0.0682997852563858,\n", "    -0.01459977775812149,\n", "    -0.07627087831497192,\n", "    0.02639031782746315,\n", "    0.009901387616991997,\n", "    -0.010885659605264664,\n", "    -0.07845363020896912,\n", "    -0.03683196380734444,\n", "    0.07640731334686279,\n", "    0.030932234600186348,\n", "    -0.018270576372742653,\n", "    -0.00726162176579237,\n", "    -0.0060408757999539375,\n", "    -0.03578563779592514,\n", "    0.050645630806684494,\n", "    0.06976093351840973,\n", "    0.04012212157249451,\n", "    -0.03445925936102867,\n", "    0.04099560156464577,\n", "    -0.006590106524527073,\n", "    0.02680322527885437,\n", "    0.04353766888380051,\n", "    -0.10779903829097748,\n", "    0.08452221751213074,\n", "    0.04010755568742752,\n", "    -0.007528107613325119,\n", "    -0.042358700186014175,\n", "    -0.022233759984374046,\n", "    -0.032726336270570755,\n", "    0.04151011258363724,\n", "    -0.008444693870842457,\n", "    0.05708244815468788,\n", "    -0.019775547087192535,\n", "    0.06383180618286133,\n", "    -0.0687061995267868,\n", "    -0.0672190710902214,\n", "    0.12178616225719452,\n", "    -0.021240346133708954,\n", "    0.024959564208984375,\n", "    -0.0676940456032753,\n", "    0.027625003829598427,\n", "    -0.07007237523794174,\n", "    -0.0192868672311306,\n", "    0.0035319242160767317,\n", "    -0.05712425708770752,\n", "    -0.06389948725700378,\n", "    0.003280498320236802,\n", "    0.004383782856166363,\n", "    -0.09214093536138535,\n", "    -0.055066365748643875,\n", "    0.046772610396146774,\n", "    -0.037242092192173004,\n", "    0.018603496253490448,\n", "    0.057261545211076736,\n", "    0.09803812950849533,\n", "    0.029605654999613762,\n", "    -0.023777233436703682,\n", "    0.05382443591952324,\n", "    0.014623275958001614,\n", "    0.055303603410720825,\n", "    -0.02868897281587124,\n", "    0.16952189803123474,\n", "    0.022101331502199173,\n", "    -0.04801800847053528,\n", "    -7.450037076381955e-33,\n", "    -0.008176477625966072,\n", "    0.035959530621767044,\n", "    -0.049974724650382996,\n", "    0.0034263557754456997,\n", "    0.01051492802798748,\n", "    -0.030328096821904182,\n", "    -0.05221966281533241,\n", "    0.059012603014707565,\n", "    0.044000811874866486,\n", "    0.1475798487663269,\n", "    0.04090909659862518,\n", "    0.0315934456884861,\n", "    0.005433476064354181,\n", "    0.030821727588772774,\n", "    -0.053444474935531616,\n", "    0.008925458416342735,\n", "    0.09802405536174774,\n", "    -0.0770888701081276,\n", "    0.026956815272569656,\n", "    -0.020032471045851707,\n", "    -0.019434859976172447,\n", "    0.04391681030392647,\n", "    -0.14396925270557404,\n", "    -0.012695979326963425,\n", "    -0.015899017453193665,\n", "    -0.0014756172895431519,\n", "    0.03272562846541405,\n", "    0.02145242877304554,\n", "    -0.006155666429549456,\n", "    0.011716466397047043,\n", "    0.005163341760635376,\n", "    0.012466753832995892,\n", "    0.021530305966734886,\n", "    -0.026088470593094826,\n", "    0.0014608631609007716,\n", "    -0.10470417886972427,\n", "    0.03600209578871727,\n", "    0.013637600466609001,\n", "    -0.0028260345570743084,\n", "    0.04169534891843796,\n", "    -0.04342346265912056,\n", "    0.037480708211660385,\n", "    0.12992210686206818,\n", "    0.0890883356332779,\n", "    -0.04286742955446243,\n", "    0.04644690081477165,\n", "    -0.020946092903614044,\n", "    -0.010475166141986847,\n", "    -0.006826065480709076,\n", "    -0.040998224169015884,\n", "    -0.03350811079144478,\n", "    0.014412553049623966,\n", "    -0.03799719735980034,\n", "    -0.11637355387210846,\n", "    0.05763569846749306,\n", "    0.07480393350124359,\n", "    0.04622877389192581,\n", "    -0.021778736263513565,\n", "    -0.037126291543245316,\n", "    0.009766897186636925,\n", "    -0.06517943739891052,\n", "    -0.0498589426279068,\n", "    0.009886309504508972,\n", "    -0.08166836202144623]}},\n", " {'_index': 'course-questions',\n", "  '_id': 'JSPvR5EBO7bpWV6zkXU7',\n", "  '_score': 27.396076,\n", "  '_source': {'text': 'Yes, we will keep all the materials after the course finishes, so you can follow the course at your own pace after it finishes.\\nYou can also continue looking at the homeworks and continue preparing for the next cohort. I guess you can also start working on your final capstone project.',\n", "   'section': 'General course-related questions',\n", "   'question': 'Course - Can I follow the course after it finishes?',\n", "   'course': 'data-engineering-zoomcamp',\n", "   'id': 'a482086d',\n", "   'question_vector': [0.014919440262019634,\n", "    -0.02898329310119152,\n", "    0.03108942322432995,\n", "    -0.018623575568199158,\n", "    -0.003799418918788433,\n", "    0.037528831511735916,\n", "    -0.08759304881095886,\n", "    -0.09858699142932892,\n", "    -0.07463330030441284,\n", "    0.027277911081910133,\n", "    0.014167607761919498,\n", "    0.09268335998058319,\n", "    0.009689852595329285,\n", "    0.05160529911518097,\n", "    -0.0056542400270700455,\n", "    -0.03466638922691345,\n", "    -0.016913648694753647,\n", "    0.025604689493775368,\n", "    0.031069012358784676,\n", "    -0.001110958168283105,\n", "    -0.07368866354227066,\n", "    0.037410758435726166,\n", "    -0.002767284633591771,\n", "    0.091824471950531,\n", "    -0.033952269703149796,\n", "    0.00687234103679657,\n", "    0.01582135260105133,\n", "    -0.03030257485806942,\n", "    -0.013535366393625736,\n", "    -0.032006338238716125,\n", "    -0.04763597249984741,\n", "    -0.05651916563510895,\n", "    0.026158392429351807,\n", "    0.00989456381648779,\n", "    -0.06629417836666107,\n", "    0.08806399255990982,\n", "    -0.0499378964304924,\n", "    -0.05122135207056999,\n", "    -0.013621298596262932,\n", "    0.04774812236428261,\n", "    -0.02542364038527012,\n", "    -0.024976273998618126,\n", "    -0.032814063131809235,\n", "    0.03880218043923378,\n", "    0.11422222852706909,\n", "    0.047101546078920364,\n", "    -0.004053175915032625,\n", "    -0.11239568889141083,\n", "    0.024110116064548492,\n", "    0.03440476581454277,\n", "    -0.029590383172035217,\n", "    -0.08897324651479721,\n", "    -0.008688169531524181,\n", "    -0.031328149139881134,\n", "    -0.013571684248745441,\n", "    0.11307869851589203,\n", "    0.04709656536579132,\n", "    0.04027118906378746,\n", "    -0.040687065571546555,\n", "    0.032822102308273315,\n", "    0.008738028816878796,\n", "    0.019359178841114044,\n", "    -0.10843069106340408,\n", "    0.0036693119909614325,\n", "    0.019084079191088676,\n", "    0.06605435907840729,\n", "    -0.025295143947005272,\n", "    0.05614544823765755,\n", "    0.09506325423717499,\n", "    0.05350533127784729,\n", "    0.041298530995845795,\n", "    0.0098950807005167,\n", "    0.046928953379392624,\n", "    0.07555388659238815,\n", "    0.0007107189740054309,\n", "    0.01862003095448017,\n", "    -0.03118596412241459,\n", "    -0.0023216293193399906,\n", "    0.05840049311518669,\n", "    -0.010860496200621128,\n", "    -0.031645290553569794,\n", "    0.0064615667797625065,\n", "    0.041444070637226105,\n", "    -0.019840216264128685,\n", "    0.020519569516181946,\n", "    -0.014158209785819054,\n", "    0.05806843191385269,\n", "    -0.010793887078762054,\n", "    0.07009785622358322,\n", "    0.0261114239692688,\n", "    0.04205259680747986,\n", "    -0.03891880437731743,\n", "    -0.044954217970371246,\n", "    0.0006072599207982421,\n", "    -0.04649839922785759,\n", "    0.05097902938723564,\n", "    -0.042405884712934494,\n", "    -0.07387083023786545,\n", "    0.04078259691596031,\n", "    -0.008733478374779224,\n", "    -0.053042229264974594,\n", "    0.0452549122273922,\n", "    -0.010635127313435078,\n", "    0.012392179109156132,\n", "    -0.01888301968574524,\n", "    -0.03651978075504303,\n", "    0.06730818003416061,\n", "    0.04645724967122078,\n", "    0.03565334901213646,\n", "    0.03126954287290573,\n", "    0.021284375339746475,\n", "    0.018556717783212662,\n", "    0.011796942912042141,\n", "    -0.04583524912595749,\n", "    0.024166518822312355,\n", "    0.074151411652565,\n", "    -0.023363105952739716,\n", "    0.04779442027211189,\n", "    0.029235782101750374,\n", "    -0.0013104728423058987,\n", "    0.055012769997119904,\n", "    0.046219728887081146,\n", "    0.0535891056060791,\n", "    -0.021089451387524605,\n", "    -0.01088434923440218,\n", "    -0.1183672845363617,\n", "    0.05564549192786217,\n", "    1.6141548002327654e-30,\n", "    -0.012926814146339893,\n", "    -0.09907639026641846,\n", "    0.012990609742701054,\n", "    -0.06886890530586243,\n", "    0.027197685092687607,\n", "    -0.01834394969046116,\n", "    0.031005222350358963,\n", "    0.04505070671439171,\n", "    -0.07734038680791855,\n", "    -0.022052744403481483,\n", "    0.039337221533060074,\n", "    0.006564008072018623,\n", "    0.034176092594861984,\n", "    -0.034446220844984055,\n", "    -0.014447920024394989,\n", "    -0.08430074900388718,\n", "    0.030181240290403366,\n", "    -0.027855122461915016,\n", "    -0.019352169707417488,\n", "    -0.0441928431391716,\n", "    0.053364820778369904,\n", "    -0.100767120718956,\n", "    -0.03335708752274513,\n", "    -0.061463262885808945,\n", "    -0.01612350158393383,\n", "    0.024097653105854988,\n", "    0.015886591747403145,\n", "    0.05289577692747116,\n", "    0.014687122777104378,\n", "    0.019429972395300865,\n", "    0.04219699278473854,\n", "    -6.809597107348964e-05,\n", "    -0.1806521713733673,\n", "    0.029176583513617516,\n", "    -0.04164939373731613,\n", "    0.025629514828324318,\n", "    0.07362590730190277,\n", "    0.03284500539302826,\n", "    0.09193301200866699,\n", "    -0.10734988003969193,\n", "    -0.04630608111619949,\n", "    -0.03379592299461365,\n", "    0.0065482198260724545,\n", "    -0.023716328665614128,\n", "    -0.03471410274505615,\n", "    0.001055413275025785,\n", "    0.04940660670399666,\n", "    -0.004740720149129629,\n", "    -0.0015028484631329775,\n", "    0.044316429644823074,\n", "    -0.029525231570005417,\n", "    -0.047416940331459045,\n", "    0.06560704112052917,\n", "    -0.1259528398513794,\n", "    0.008380877785384655,\n", "    -0.011959926225244999,\n", "    0.04588572308421135,\n", "    0.042144112288951874,\n", "    -0.09273923933506012,\n", "    -0.031510695815086365,\n", "    0.06169384717941284,\n", "    0.02299266867339611,\n", "    -0.08410970121622086,\n", "    0.07433327287435532,\n", "    -0.09618377685546875,\n", "    0.01865268684923649,\n", "    -0.08129362016916275,\n", "    -0.10259785503149033,\n", "    0.050711244344711304,\n", "    -0.06275124847888947,\n", "    -0.09125278890132904,\n", "    0.0041145323775708675,\n", "    -0.09987767785787582,\n", "    0.011697094887495041,\n", "    0.05911152809858322,\n", "    0.02310817874968052,\n", "    -0.09326794743537903,\n", "    0.009172018617391586,\n", "    0.09838606417179108,\n", "    -0.0472237765789032,\n", "    -0.01547615323215723,\n", "    -0.023185566067695618,\n", "    -0.0506463423371315,\n", "    0.01797054149210453,\n", "    0.001797069562599063,\n", "    0.05333495885133743,\n", "    0.01690663956105709,\n", "    -0.008003778755664825,\n", "    0.03020637296140194,\n", "    0.042582638561725616,\n", "    -0.06319618225097656,\n", "    -0.004492723848670721,\n", "    0.007430401165038347,\n", "    0.12391887605190277,\n", "    0.0850818008184433,\n", "    -1.8263802665519734e-33,\n", "    0.11109014600515366,\n", "    0.010005605407059193,\n", "    0.05576727166771889,\n", "    0.04018433392047882,\n", "    0.01750756800174713,\n", "    -0.03681189566850662,\n", "    -0.024166960269212723,\n", "    0.022556200623512268,\n", "    -0.012280836701393127,\n", "    0.06184542551636696,\n", "    -0.012835284695029259,\n", "    -0.006323255132883787,\n", "    -0.046733308583498,\n", "    0.020106296986341476,\n", "    0.0014350133715197444,\n", "    -0.031715407967567444,\n", "    -0.014256776310503483,\n", "    -0.015420710667967796,\n", "    -0.04352935776114464,\n", "    -0.0066804732196033,\n", "    -0.06346602737903595,\n", "    -0.007820703089237213,\n", "    0.019784949719905853,\n", "    -0.09509260207414627,\n", "    0.001255011186003685,\n", "    0.027650484815239906,\n", "    0.07705020159482956,\n", "    -0.0032652062363922596,\n", "    -0.0035533257760107517,\n", "    -0.0017884792760014534,\n", "    0.11395933479070663,\n", "    -0.06138397008180618,\n", "    0.016615092754364014,\n", "    -0.06818435341119766,\n", "    -0.0342429019510746,\n", "    0.0729828029870987,\n", "    -0.013544873334467411,\n", "    -0.026551727205514908,\n", "    -0.07793626189231873,\n", "    0.09468597173690796,\n", "    0.08522746711969376,\n", "    -0.024148860946297646,\n", "    -0.010575828142464161,\n", "    -0.04675719887018204,\n", "    0.02477382682263851,\n", "    -0.022871021181344986,\n", "    0.09371232241392136,\n", "    0.01736854575574398,\n", "    -0.037029337137937546,\n", "    0.0431295670568943,\n", "    -0.04242396727204323,\n", "    -0.006884131580591202,\n", "    0.00016837130533531308,\n", "    -0.07815661281347275,\n", "    0.06675101071596146,\n", "    0.07220030575990677,\n", "    -0.04072890430688858,\n", "    -0.014436199329793453,\n", "    -0.08130265772342682,\n", "    -0.0657447800040245,\n", "    0.03598272427916527,\n", "    0.032933954149484634,\n", "    0.0316169373691082,\n", "    -0.017842303961515427,\n", "    0.06906383484601974,\n", "    0.0022461481858044863,\n", "    -0.02130492962896824,\n", "    0.1127161830663681,\n", "    0.0016089071286842227,\n", "    0.017895745113492012,\n", "    -0.054485563188791275,\n", "    0.05565160885453224,\n", "    -0.07278821617364883,\n", "    -0.04928164556622505,\n", "    0.04710782691836357,\n", "    -0.10587996244430542,\n", "    0.01838984712958336,\n", "    -0.022930486127734184,\n", "    -0.010272134095430374,\n", "    0.015679696574807167,\n", "    -0.03137276694178581,\n", "    0.027760876342654228,\n", "    -0.0645846500992775,\n", "    0.028828168287873268,\n", "    0.018049033358693123,\n", "    0.04260006919503212,\n", "    0.007748615462332964,\n", "    -0.03769597038626671,\n", "    0.0465247817337513,\n", "    0.03661850094795227,\n", "    0.041697919368743896,\n", "    0.0059996116906404495,\n", "    0.0841933935880661,\n", "    -0.021909616887569427,\n", "    -0.04881252720952034,\n", "    -6.40646110971276e-33,\n", "    -0.09371776133775711,\n", "    0.01108815148472786,\n", "    0.05198900029063225,\n", "    0.054757796227931976,\n", "    0.011515457183122635,\n", "    0.013861313462257385,\n", "    0.009503811597824097,\n", "    0.005119521636515856,\n", "    -0.024116193875670433,\n", "    0.08830251544713974,\n", "    -0.025906916707754135,\n", "    0.1026420071721077,\n", "    0.050942402333021164,\n", "    0.04202285781502724,\n", "    -0.045086152851581573,\n", "    -0.010068435221910477,\n", "    0.0865003913640976,\n", "    -0.00724734365940094,\n", "    -0.013949643820524216,\n", "    0.03116021677851677,\n", "    -0.001305346260778606,\n", "    0.03555729240179062,\n", "    -0.0008269736426882446,\n", "    0.03160114586353302,\n", "    -0.04237774387001991,\n", "    -0.005646653939038515,\n", "    0.04764480143785477,\n", "    -0.004108506254851818,\n", "    -0.025719959288835526,\n", "    0.0297499131411314,\n", "    0.02536889538168907,\n", "    0.021595200523734093,\n", "    0.014964804984629154,\n", "    0.022899286821484566,\n", "    -0.03323356807231903,\n", "    -0.12309233844280243,\n", "    0.08295834809541702,\n", "    0.03593481332063675,\n", "    0.03803713247179985,\n", "    0.08161461353302002,\n", "    -0.04848653823137283,\n", "    -0.05123094469308853,\n", "    0.09920376539230347,\n", "    0.13132989406585693,\n", "    0.04541248455643654,\n", "    0.019051438197493553,\n", "    -0.07856002449989319,\n", "    -0.03114851750433445,\n", "    0.01955844834446907,\n", "    -0.06348028033971786,\n", "    -0.039638713002204895,\n", "    -0.001958850072696805,\n", "    -0.09916866570711136,\n", "    -0.038827262818813324,\n", "    0.04233460873365402,\n", "    0.04571111872792244,\n", "    0.03704991191625595,\n", "    0.010128541849553585,\n", "    -0.1109614372253418,\n", "    0.003979129251092672,\n", "    -0.054381925612688065,\n", "    -0.12614154815673828,\n", "    -0.018048295751214027,\n", "    -0.02967592142522335],\n", "   'text_vector': [-0.05716966837644577,\n", "    0.023199932649731636,\n", "    0.04556477442383766,\n", "    -0.014644552022218704,\n", "    0.03988425433635712,\n", "    0.02101348526775837,\n", "    -0.10055272281169891,\n", "    -0.07310185581445694,\n", "    -0.12441075593233109,\n", "    -0.011179528199136257,\n", "    -0.0032924385741353035,\n", "    0.09292680770158768,\n", "    -0.038658637553453445,\n", "    -0.026109440252184868,\n", "    -0.05509275197982788,\n", "    0.03897976502776146,\n", "    -0.025895515456795692,\n", "    -0.03459259867668152,\n", "    -0.007996092550456524,\n", "    -0.027532435953617096,\n", "    -0.09479675441980362,\n", "    0.04020942002534866,\n", "    0.01913539320230484,\n", "    0.055650677531957626,\n", "    0.01220038253813982,\n", "    0.09195056557655334,\n", "    -0.052345748990774155,\n", "    -0.09112466126680374,\n", "    0.04319502413272858,\n", "    -0.039984580129384995,\n", "    -0.027045918628573418,\n", "    -0.03465196117758751,\n", "    0.009310339577496052,\n", "    -0.007842794992029667,\n", "    0.04080875962972641,\n", "    0.13159245252609253,\n", "    0.03070521168410778,\n", "    0.02562667429447174,\n", "    0.025526998564600945,\n", "    0.053224094212055206,\n", "    -0.028910424560308456,\n", "    -0.02720065601170063,\n", "    -0.05486370250582695,\n", "    0.04544418677687645,\n", "    0.07013864815235138,\n", "    -0.02983533963561058,\n", "    0.015854882076382637,\n", "    -0.13473816215991974,\n", "    -0.028098616749048233,\n", "    0.06576818227767944,\n", "    -0.011645089834928513,\n", "    -0.06531910598278046,\n", "    -0.01700867898762226,\n", "    -0.063934825360775,\n", "    -0.040529172867536545,\n", "    0.14450928568840027,\n", "    0.0730757862329483,\n", "    -0.028623955324292183,\n", "    -0.0299663282930851,\n", "    0.007420662324875593,\n", "    -0.0014160871505737305,\n", "    0.016325894743204117,\n", "    -0.06137715280056,\n", "    -0.00339551386423409,\n", "    0.11337704211473465,\n", "    0.10748498886823654,\n", "    0.019067399203777313,\n", "    0.09697705507278442,\n", "    0.07190084457397461,\n", "    0.06556889414787292,\n", "    -0.005868683569133282,\n", "    0.05175139009952545,\n", "    0.07583755254745483,\n", "    0.031682271510362625,\n", "    0.04898373410105705,\n", "    0.03751595690846443,\n", "    -0.045432500541210175,\n", "    -0.015654880553483963,\n", "    0.011877664364874363,\n", "    -0.01060640998184681,\n", "    -0.04718426614999771,\n", "    -0.012453866191208363,\n", "    0.05522860959172249,\n", "    -0.07450563460588455,\n", "    -0.02454802207648754,\n", "    -0.046053264290094376,\n", "    0.04122912138700485,\n", "    0.0190135408192873,\n", "    0.0637330561876297,\n", "    -0.01681227795779705,\n", "    -0.004822891671210527,\n", "    -0.002002293011173606,\n", "    0.001057428540661931,\n", "    0.06286103278398514,\n", "    -0.1021718680858612,\n", "    0.07094483077526093,\n", "    -0.039171069860458374,\n", "    0.010297391563653946,\n", "    0.07860058546066284,\n", "    -0.037493620067834854,\n", "    -0.02882353775203228,\n", "    0.03661341592669487,\n", "    -0.0034001143649220467,\n", "    -0.02779027260839939,\n", "    -0.07603766769170761,\n", "    -0.09504926204681396,\n", "    -0.008272206410765648,\n", "    -0.005001944024115801,\n", "    -0.016779165714979172,\n", "    -0.023155882954597473,\n", "    0.04316195100545883,\n", "    0.04460151866078377,\n", "    -0.060635652393102646,\n", "    -0.013915099203586578,\n", "    0.005567493382841349,\n", "    0.057396333664655685,\n", "    0.0035060830414295197,\n", "    -0.02121916599571705,\n", "    0.03881014138460159,\n", "    0.056894365698099136,\n", "    0.05809176340699196,\n", "    0.12966711819171906,\n", "    0.038325853645801544,\n", "    -0.032588545233011246,\n", "    -0.012629981152713299,\n", "    -0.09272641688585281,\n", "    0.017788616940379143,\n", "    6.337198660960968e-31,\n", "    -0.0282196756452322,\n", "    -0.013339182361960411,\n", "    0.014646184630692005,\n", "    -0.024135401472449303,\n", "    -0.011054201982915401,\n", "    -0.04125292971730232,\n", "    0.06640224158763885,\n", "    0.06592580676078796,\n", "    -0.06988605111837387,\n", "    -0.05509721487760544,\n", "    0.08494782447814941,\n", "    0.07829619199037552,\n", "    -0.01036844588816166,\n", "    0.02402576059103012,\n", "    -0.013435401022434235,\n", "    -0.10948073118925095,\n", "    0.04550623893737793,\n", "    -0.01047533005475998,\n", "    -0.015707530081272125,\n", "    -0.08810663968324661,\n", "    -0.00682866433635354,\n", "    -0.049956824630498886,\n", "    -0.04098493605852127,\n", "    -0.02084096148610115,\n", "    0.06670032441616058,\n", "    0.05816588178277016,\n", "    0.03699944540858269,\n", "    0.06512974947690964,\n", "    -0.09376855194568634,\n", "    0.02040378376841545,\n", "    0.019680041819810867,\n", "    0.03969835489988327,\n", "    -0.1240212470293045,\n", "    0.04139822721481323,\n", "    -0.028197702020406723,\n", "    0.09567280858755112,\n", "    -0.03153552860021591,\n", "    0.012852084822952747,\n", "    0.07986454665660858,\n", "    -0.09120646864175797,\n", "    -0.045138195157051086,\n", "    0.02838045172393322,\n", "    0.04776502773165703,\n", "    0.0003468134964350611,\n", "    0.005830523557960987,\n", "    -0.032491400837898254,\n", "    0.06801646947860718,\n", "    0.025441208854317665,\n", "    0.00473517831414938,\n", "    -0.004694869741797447,\n", "    0.0007257883553393185,\n", "    -0.032765042036771774,\n", "    0.01581275463104248,\n", "    -0.03329108655452728,\n", "    0.013769503682851791,\n", "    -0.01297691185027361,\n", "    0.05286482349038124,\n", "    0.009914190508425236,\n", "    -0.032421842217445374,\n", "    -0.059114374220371246,\n", "    0.13501262664794922,\n", "    0.028223931789398193,\n", "    -0.0674036294221878,\n", "    0.10474495589733124,\n", "    -0.04033689200878143,\n", "    0.08090350776910782,\n", "    -0.0216985996812582,\n", "    -0.05593032389879227,\n", "    0.03435142710804939,\n", "    -0.03948921710252762,\n", "    -0.10363870859146118,\n", "    -0.05324206128716469,\n", "    -0.060439325869083405,\n", "    0.05318988114595413,\n", "    0.018018728122115135,\n", "    0.021231848746538162,\n", "    -0.05627559497952461,\n", "    -0.02766512706875801,\n", "    0.017557356506586075,\n", "    -0.037738025188446045,\n", "    0.04145714268088341,\n", "    0.028750702738761902,\n", "    -0.06465411186218262,\n", "    -0.09339544177055359,\n", "    -0.003360215574502945,\n", "    -0.05031001940369606,\n", "    0.053743936121463776,\n", "    0.011854474432766438,\n", "    0.05902943015098572,\n", "    0.02923475205898285,\n", "    -0.019189205020666122,\n", "    -0.02361384406685829,\n", "    -0.004364061169326305,\n", "    0.04763086140155792,\n", "    -0.013160618022084236,\n", "    -3.746833876632316e-33,\n", "    0.042289432138204575,\n", "    -0.002858405467122793,\n", "    -0.016859469935297966,\n", "    0.024557700380682945,\n", "    0.02256995625793934,\n", "    -0.025691458955407143,\n", "    -0.013888055458664894,\n", "    -0.0004586365248542279,\n", "    -0.02529173158109188,\n", "    0.06084493175148964,\n", "    0.024282297119498253,\n", "    -0.03623524308204651,\n", "    -0.03113865666091442,\n", "    -0.014560963958501816,\n", "    -0.10014686733484268,\n", "    -0.01010033767670393,\n", "    0.046172287315130234,\n", "    -0.004697117488831282,\n", "    0.06610298156738281,\n", "    -0.0644969716668129,\n", "    -0.0601954385638237,\n", "    0.019845351576805115,\n", "    -0.04011629894375801,\n", "    -0.059398554265499115,\n", "    -0.008525929413735867,\n", "    -0.006921431515365839,\n", "    -0.04986819997429848,\n", "    -0.08497216552495956,\n", "    0.010382372885942459,\n", "    0.04481393098831177,\n", "    0.04395332187414169,\n", "    -0.09091071784496307,\n", "    -0.023815641179680824,\n", "    -0.07729433476924896,\n", "    -0.03854841738939285,\n", "    -0.015344703570008278,\n", "    0.03958967700600624,\n", "    -0.03599300608038902,\n", "    -0.03933600336313248,\n", "    0.06649219989776611,\n", "    0.053685612976551056,\n", "    -0.00018855174130294472,\n", "    -0.03843773156404495,\n", "    -0.011220169253647327,\n", "    0.017313702031970024,\n", "    -0.016394224017858505,\n", "    0.0452963262796402,\n", "    0.03420951962471008,\n", "    -0.04365843906998634,\n", "    0.008848391473293304,\n", "    0.022712012752890587,\n", "    0.010320920497179031,\n", "    0.022653020918369293,\n", "    -0.13478751480579376,\n", "    0.0678301453590393,\n", "    0.09130512177944183,\n", "    -0.006710021290928125,\n", "    -0.060037143528461456,\n", "    -0.0817192941904068,\n", "    0.022772327065467834,\n", "    0.044564709067344666,\n", "    0.056733738631010056,\n", "    0.022642651572823524,\n", "    -0.021465247496962547,\n", "    0.0669349804520607,\n", "    0.004649037960916758,\n", "    -0.00796605832874775,\n", "    0.03347973898053169,\n", "    -0.06804277747869492,\n", "    0.040545038878917694,\n", "    -0.038191165775060654,\n", "    0.039363421499729156,\n", "    -0.08072677254676819,\n", "    -0.023217901587486267,\n", "    0.028055710718035698,\n", "    -0.035935282707214355,\n", "    0.009602892212569714,\n", "    0.006388664711266756,\n", "    -0.01728029176592827,\n", "    -0.04850434139370918,\n", "    -0.042884040623903275,\n", "    -0.01505054160952568,\n", "    -0.06108202040195465,\n", "    0.023274358361959457,\n", "    0.029980700463056564,\n", "    -0.03586708754301071,\n", "    0.009101593866944313,\n", "    -0.040423475205898285,\n", "    0.050306208431720734,\n", "    0.021637873724102974,\n", "    0.031812604516744614,\n", "    -0.037254005670547485,\n", "    0.04098367318511009,\n", "    0.03431050479412079,\n", "    -0.031827766448259354,\n", "    -5.6571326848894924e-33,\n", "    -0.03822549432516098,\n", "    0.052901141345500946,\n", "    0.016873164102435112,\n", "    0.01996542699635029,\n", "    -0.03929182142019272,\n", "    0.0022660703398287296,\n", "    0.03513273969292641,\n", "    0.026942839846014977,\n", "    0.02082388661801815,\n", "    0.07219769060611725,\n", "    -0.003059845883399248,\n", "    0.044497471302747726,\n", "    0.026353230699896812,\n", "    0.06992432475090027,\n", "    -0.11931582540273666,\n", "    0.05143243819475174,\n", "    0.10614117980003357,\n", "    -0.08515371382236481,\n", "    -0.02855531871318817,\n", "    -0.024342728778719902,\n", "    0.05627827346324921,\n", "    0.0794096365571022,\n", "    0.028250914067029953,\n", "    0.010106769390404224,\n", "    -0.02555561438202858,\n", "    0.008168932050466537,\n", "    0.0648384839296341,\n", "    0.02261068858206272,\n", "    -0.026393266394734383,\n", "    0.03199497237801552,\n", "    0.0009267002460546792,\n", "    -0.018074097111821175,\n", "    0.0015146153746172786,\n", "    -0.0020757101010531187,\n", "    0.013061800971627235,\n", "    -0.09799354523420334,\n", "    0.03370968997478485,\n", "    0.06402980536222458,\n", "    0.024914968758821487,\n", "    0.0971623882651329,\n", "    -0.06179871782660484,\n", "    -0.024611983448266983,\n", "    0.02726099081337452,\n", "    0.10743234306573868,\n", "    0.07072935998439789,\n", "    -0.016373688355088234,\n", "    -0.10925512760877609,\n", "    -0.0075975628569722176,\n", "    -0.012427132576704025,\n", "    0.004498232621699572,\n", "    -0.013344952836632729,\n", "    -0.06797166168689728,\n", "    -0.029735546559095383,\n", "    -0.05387607589364052,\n", "    0.08771764487028122,\n", "    0.053692590445280075,\n", "    0.08204417675733566,\n", "    0.05273288115859032,\n", "    -0.04688340798020363,\n", "    -0.039446115493774414,\n", "    -0.04715879634022713,\n", "    -0.11524023115634918,\n", "    -0.011407773941755295,\n", "    -0.02946900576353073],\n", "   'question_text_vector': [-0.05189281329512596,\n", "    0.0044535365886986256,\n", "    0.030934492126107216,\n", "    -0.011301929131150246,\n", "    0.01948142983019352,\n", "    0.03526098653674126,\n", "    -0.10404135286808014,\n", "    -0.07685092836618423,\n", "    -0.11506534367799759,\n", "    -0.0030712808948010206,\n", "    0.019074352458119392,\n", "    0.06090925633907318,\n", "    -0.021014919504523277,\n", "    -0.03155580908060074,\n", "    -0.04986543953418732,\n", "    0.021092329174280167,\n", "    -0.014987887814640999,\n", "    -0.018495995551347733,\n", "    0.001843583188019693,\n", "    -0.03857149928808212,\n", "    -0.08266563713550568,\n", "    0.04421258717775345,\n", "    0.023393945768475533,\n", "    0.08282395452260971,\n", "    0.0061532580293715,\n", "    0.07858364284038544,\n", "    -0.02853085845708847,\n", "    -0.07038842886686325,\n", "    0.019194193184375763,\n", "    -0.030024752020835876,\n", "    -0.04029427096247673,\n", "    -0.039080504328012466,\n", "    0.007786852773278952,\n", "    -0.009430000558495522,\n", "    -0.009189985692501068,\n", "    0.12635336816310883,\n", "    -0.0009038506541401148,\n", "    -0.0032537446822971106,\n", "    0.002798383589833975,\n", "    0.048777200281620026,\n", "    -0.02558264695107937,\n", "    -0.015103756450116634,\n", "    -0.0518408864736557,\n", "    0.05351085588335991,\n", "    0.08773495256900787,\n", "    -0.015532705001533031,\n", "    0.012056601233780384,\n", "    -0.12651239335536957,\n", "    -0.00923212617635727,\n", "    0.06041237711906433,\n", "    -0.03235825523734093,\n", "    -0.09160459786653519,\n", "    -0.04169590771198273,\n", "    -0.047338880598545074,\n", "    -0.05344650521874428,\n", "    0.13900016248226166,\n", "    0.05104067921638489,\n", "    0.005390174221247435,\n", "    -0.02611834555864334,\n", "    -0.0033728163689374924,\n", "    0.014931573532521725,\n", "    0.019648101180791855,\n", "    -0.10199952125549316,\n", "    0.011767186224460602,\n", "    0.076835498213768,\n", "    0.1154019758105278,\n", "    0.007596833165735006,\n", "    0.08796757459640503,\n", "    0.09394229203462601,\n", "    0.06618998199701309,\n", "    0.022108502686023712,\n", "    0.05277495086193085,\n", "    0.06467434018850327,\n", "    0.044840842485427856,\n", "    0.017752306535840034,\n", "    0.010832161642611027,\n", "    -0.03870399668812752,\n", "    -0.019805626943707466,\n", "    0.03541053459048271,\n", "    -0.025717347860336304,\n", "    -0.05455866456031799,\n", "    -0.0015204472001641989,\n", "    0.05458538979291916,\n", "    -0.03812490403652191,\n", "    0.017609205096960068,\n", "    -0.0298930574208498,\n", "    0.05312841758131981,\n", "    0.003653655294328928,\n", "    0.04946134611964226,\n", "    0.02042790688574314,\n", "    -0.0049856542609632015,\n", "    -0.004128170199692249,\n", "    -0.018315216526389122,\n", "    0.0522560216486454,\n", "    -0.0785742923617363,\n", "    0.05744974687695503,\n", "    -0.04267682135105133,\n", "    -0.014998643659055233,\n", "    0.08880485594272614,\n", "    -0.03471045196056366,\n", "    -0.034510307013988495,\n", "    0.017083076760172844,\n", "    -0.01117787417024374,\n", "    -0.008750932291150093,\n", "    -0.05315800756216049,\n", "    -0.07126312702894211,\n", "    -0.0010020871413871646,\n", "    0.005752895958721638,\n", "    -0.0013070704881101847,\n", "    -0.009482699446380138,\n", "    0.033452413976192474,\n", "    0.0317874476313591,\n", "    -0.05077427253127098,\n", "    -0.04424652084708214,\n", "    0.02061772346496582,\n", "    0.06765326112508774,\n", "    0.00218611815944314,\n", "    0.01822589710354805,\n", "    0.033797215670347214,\n", "    0.05720905959606171,\n", "    0.059736862778663635,\n", "    0.12862038612365723,\n", "    0.02333255298435688,\n", "    -0.030456803739070892,\n", "    -0.028849497437477112,\n", "    -0.09659168124198914,\n", "    0.039704203605651855,\n", "    6.320911011235974e-31,\n", "    -0.02620183303952217,\n", "    -0.024283159524202347,\n", "    0.02218100056052208,\n", "    -0.03553420305252075,\n", "    -0.006289215758442879,\n", "    -0.026796074584126472,\n", "    0.06760900467634201,\n", "    0.08607833832502365,\n", "    -0.10746902972459793,\n", "    -0.03580186516046524,\n", "    0.09631156921386719,\n", "    0.06059681624174118,\n", "    0.01706114038825035,\n", "    0.007978846319019794,\n", "    -0.037489525973796844,\n", "    -0.12245053797960281,\n", "    0.04750479757785797,\n", "    -0.03022197261452675,\n", "    -0.035586901009082794,\n", "    -0.09836810827255249,\n", "    0.009245004504919052,\n", "    -0.05557246506214142,\n", "    -0.03893318027257919,\n", "    -0.04080447927117348,\n", "    0.032107383012771606,\n", "    0.045184940099716187,\n", "    0.031110478565096855,\n", "    0.07151266187429428,\n", "    -0.05019819736480713,\n", "    0.026738571003079414,\n", "    0.05597410723567009,\n", "    0.02114413119852543,\n", "    -0.14009064435958862,\n", "    0.04948914796113968,\n", "    -0.056104402989149094,\n", "    0.06888742744922638,\n", "    0.00962473452091217,\n", "    0.005787820089608431,\n", "    0.08210121840238571,\n", "    -0.10567192733287811,\n", "    -0.055679671466350555,\n", "    0.003380882553756237,\n", "    0.028053035959601402,\n", "    -0.010604342445731163,\n", "    -0.007091019302606583,\n", "    -0.012124883942306042,\n", "    0.058779794722795486,\n", "    0.023731278255581856,\n", "    0.029497554525732994,\n", "    0.03444867953658104,\n", "    0.0016683932626619935,\n", "    -0.05333905294537544,\n", "    0.01609579659998417,\n", "    -0.0633801743388176,\n", "    0.02049107849597931,\n", "    -0.018982263281941414,\n", "    0.03212408721446991,\n", "    0.04099516570568085,\n", "    -0.05795024335384369,\n", "    -0.04987962543964386,\n", "    0.14670810103416443,\n", "    0.020187515765428543,\n", "    -0.07117711007595062,\n", "    0.09184636175632477,\n", "    -0.06051020324230194,\n", "    0.05574960261583328,\n", "    -0.05181744694709778,\n", "    -0.08505421876907349,\n", "    0.041196417063474655,\n", "    -0.06345665454864502,\n", "    -0.10225391387939453,\n", "    -0.024437228217720985,\n", "    -0.07231421768665314,\n", "    0.05656289681792259,\n", "    0.03502892702817917,\n", "    0.022951465100049973,\n", "    -0.08939804881811142,\n", "    -0.020481549203395844,\n", "    0.046205613762140274,\n", "    -0.04364457353949547,\n", "    -0.010821614414453506,\n", "    0.02363581955432892,\n", "    -0.08214808255434036,\n", "    -0.06858348846435547,\n", "    -0.03759782388806343,\n", "    -0.004741326905786991,\n", "    0.03339089825749397,\n", "    0.016024766489863396,\n", "    0.040081143379211426,\n", "    0.04812765121459961,\n", "    -0.026197420433163643,\n", "    -0.0069372341968119144,\n", "    -0.011926802806556225,\n", "    0.08398410677909851,\n", "    0.039376843720674515,\n", "    -3.946196616505822e-33,\n", "    0.08743765950202942,\n", "    -0.0014301877235993743,\n", "    0.02121904492378235,\n", "    0.010364367626607418,\n", "    0.026900073513388634,\n", "    -0.027928097173571587,\n", "    -0.02341926470398903,\n", "    -0.012381092645227909,\n", "    -0.00571216968819499,\n", "    0.06878707557916641,\n", "    0.014439268037676811,\n", "    -0.012787370942533016,\n", "    -0.02921956218779087,\n", "    -0.018756993114948273,\n", "    -0.1014045998454094,\n", "    -0.009080869145691395,\n", "    0.015041341073811054,\n", "    -0.008544343523681164,\n", "    0.036973610520362854,\n", "    -0.03833197429776192,\n", "    -0.07068337500095367,\n", "    -0.010424529202282429,\n", "    -0.023197252303361893,\n", "    -0.0788075178861618,\n", "    -0.025245072320103645,\n", "    -0.01571975089609623,\n", "    -0.013170127756893635,\n", "    -0.051471613347530365,\n", "    0.02758825197815895,\n", "    0.02133561111986637,\n", "    0.07616913318634033,\n", "    -0.07709170132875443,\n", "    0.030615106225013733,\n", "    -0.08676531165838242,\n", "    -0.056035879999399185,\n", "    0.03705837205052376,\n", "    0.022239742800593376,\n", "    -0.030111851170659065,\n", "    -0.039005350321531296,\n", "    0.058858223259449005,\n", "    0.059944313019514084,\n", "    -0.008819418959319592,\n", "    -0.023421213030815125,\n", "    -0.04915929213166237,\n", "    0.02635284885764122,\n", "    -0.017637964338064194,\n", "    0.07638349384069443,\n", "    0.026122424751520157,\n", "    -0.07017671316862106,\n", "    0.04016675800085068,\n", "    -0.003847803920507431,\n", "    -0.02395435981452465,\n", "    0.005653477273881435,\n", "    -0.1144944354891777,\n", "    0.056849755346775055,\n", "    0.09899236261844635,\n", "    -0.02702978067100048,\n", "    -0.06148971989750862,\n", "    -0.0791778489947319,\n", "    -0.014653814025223255,\n", "    0.0522446408867836,\n", "    0.04019998386502266,\n", "    0.03357080742716789,\n", "    -0.003090454265475273,\n", "    0.051156651228666306,\n", "    -0.003407456446439028,\n", "    -0.03476564213633537,\n", "    0.04537727311253548,\n", "    -0.0402136892080307,\n", "    0.02758742682635784,\n", "    -0.08633355796337128,\n", "    0.07175254821777344,\n", "    -0.06866475939750671,\n", "    -0.03763556480407715,\n", "    0.04879104718565941,\n", "    -0.06432226300239563,\n", "    -0.024867132306098938,\n", "    -0.018303116783499718,\n", "    -0.025088990107178688,\n", "    -0.037472281605005264,\n", "    -0.03016112558543682,\n", "    -0.0029214192181825638,\n", "    -0.05949009954929352,\n", "    0.0440610833466053,\n", "    0.0014802570221945643,\n", "    0.0017790567362681031,\n", "    0.029229560866951942,\n", "    -0.04209981486201286,\n", "    0.05251374468207359,\n", "    0.022336024791002274,\n", "    0.03792700543999672,\n", "    -0.017459724098443985,\n", "    0.06324008107185364,\n", "    0.01418092381209135,\n", "    -0.024803228676319122,\n", "    -5.052488145527263e-33,\n", "    -0.05015501007437706,\n", "    0.038867365568876266,\n", "    0.028799619525671005,\n", "    0.011213281191885471,\n", "    -0.025974148884415627,\n", "    0.033393748104572296,\n", "    0.034996937960386276,\n", "    0.02940882183611393,\n", "    0.004199218936264515,\n", "    0.05992317572236061,\n", "    -0.04379941523075104,\n", "    0.07963687181472778,\n", "    0.0582972951233387,\n", "    0.07231665402650833,\n", "    -0.11524669080972672,\n", "    0.004767945501953363,\n", "    0.10276663303375244,\n", "    -0.06796076148748398,\n", "    -0.016494430601596832,\n", "    -0.009814855642616749,\n", "    0.036158252507448196,\n", "    0.04817209765315056,\n", "    0.03997493162751198,\n", "    0.012765409424901009,\n", "    -0.030935734510421753,\n", "    0.003925783094018698,\n", "    0.04888150840997696,\n", "    0.015838230028748512,\n", "    -0.030842548236250877,\n", "    0.03250474855303764,\n", "    0.015176537446677685,\n", "    0.007086917292326689,\n", "    7.746829942334443e-05,\n", "    0.0029177924152463675,\n", "    0.005712925922125578,\n", "    -0.09595518559217453,\n", "    0.06850320100784302,\n", "    0.06902840733528137,\n", "    0.0340275838971138,\n", "    0.11063732206821442,\n", "    -0.0515194870531559,\n", "    -0.02611475996673107,\n", "    0.05339638888835907,\n", "    0.10411292314529419,\n", "    0.0613788440823555,\n", "    0.006636189296841621,\n", "    -0.0787401795387268,\n", "    -0.007724418770521879,\n", "    0.0028435324784368277,\n", "    0.0036209379322826862,\n", "    -0.019554078578948975,\n", "    -0.05332299694418907,\n", "    -0.045514922589063644,\n", "    -0.04470349848270416,\n", "    0.07339801639318466,\n", "    0.*****************,\n", "    0.*****************,\n", "    0.****************,\n", "    -0.*****************,\n", "    -0.004166422877460718,\n", "    -0.057406120002269745,\n", "    -0.*****************,\n", "    -0.017595354467630386,\n", "    -0.*****************]}},\n", " {'_index': 'course-questions',\n", "  '_id': 'IiPvR5EBO7bpWV6zkHXB',\n", "  '_score': 21.920742,\n", "  '_source': {'text': 'You can start by installing and setting up all the dependencies and requirements:\\nGoogle cloud account\\nGoogle Cloud SDK\\nPython 3 (installed with Anaconda)\\nTerraform\\nGit\\nLook over the prerequisites and syllabus to see if you are comfortable with these subjects.',\n", "   'section': 'General course-related questions',\n", "   'question': 'Course - What can I do before the course starts?',\n", "   'course': 'data-engineering-zoomcamp',\n", "   'id': '63394d91',\n", "   'question_vector': [0.027007848024368286,\n", "    0.*****************,\n", "    0.025388596579432487,\n", "    0.016639679670333862,\n", "    -0.013552735559642315,\n", "    0.*****************,\n", "    -0.047821447253227234,\n", "    -0.*****************,\n", "    -0.*****************,\n", "    0.033164896070957184,\n", "    -0.011010522022843361,\n", "    0.004639197140932083,\n", "    -0.017276523634791374,\n", "    0.051505547016859055,\n", "    -0.*****************,\n", "    -0.****************,\n", "    -0.012291721068322659,\n", "    -0.0022758885752409697,\n", "    0.****************,\n", "    0.0010828030062839389,\n", "    -0.04344594106078148,\n", "    0.03386618196964264,\n", "    -0.03125673159956932,\n", "    0.033904485404491425,\n", "    -0.0031690949108451605,\n", "    0.014201086945831776,\n", "    0.03489982336759567,\n", "    0.013936114497482777,\n", "    0.019683288410305977,\n", "    -0.025727519765496254,\n", "    -0.036871086806058884,\n", "    -0.02612297050654888,\n", "    0.05903607979416847,\n", "    0.03722430020570755,\n", "    -0.03364836424589157,\n", "    0.08995400369167328,\n", "    0.007116375956684351,\n", "    -0.04883696511387825,\n", "    0.024004613980650902,\n", "    0.0476495623588562,\n", "    -0.02554873563349247,\n", "    0.003639460541307926,\n", "    -0.006104886066168547,\n", "    0.04378731548786163,\n", "    0.09591545909643173,\n", "    0.024734707549214363,\n", "    0.035921361297369,\n", "    -0.06579617410898209,\n", "    0.07869122177362442,\n", "    -0.025817491114139557,\n", "    -0.018619129434227943,\n", "    -0.024009451270103455,\n", "    -0.04562510922551155,\n", "    -0.09488069266080856,\n", "    -0.0213918499648571,\n", "    0.08338321000337601,\n", "    0.018052250146865845,\n", "    0.04448311775922775,\n", "    -0.053844016045331955,\n", "    0.030691903084516525,\n", "    -0.10957671701908112,\n", "    0.0002567713090684265,\n", "    -0.08059626817703247,\n", "    0.023535169661045074,\n", "    -0.02732451818883419,\n", "    0.042048145085573196,\n", "    0.011671063490211964,\n", "    0.14106222987174988,\n", "    0.09585686773061752,\n", "    0.04131311923265457,\n", "    0.016869358718395233,\n", "    0.0317256897687912,\n", "    0.024959277361631393,\n", "    0.04534865915775299,\n", "    0.044790953397750854,\n", "    -0.017728300765156746,\n", "    -0.014146048575639725,\n", "    -0.008133262395858765,\n", "    0.0919746682047844,\n", "    -0.03414381667971611,\n", "    -0.03157798945903778,\n", "    0.028653809800744057,\n", "    -0.009711471386253834,\n", "    0.02811221405863762,\n", "    -0.03531937301158905,\n", "    0.0009131394326686859,\n", "    0.08538585901260376,\n", "    0.026402492076158524,\n", "    0.020964890718460083,\n", "    0.0004234316584188491,\n", "    0.04226883500814438,\n", "    -0.10182550549507141,\n", "    -0.04517577588558197,\n", "    0.06451833248138428,\n", "    -0.02242698334157467,\n", "    0.0648200511932373,\n", "    -0.015449807047843933,\n", "    -0.06784822791814804,\n", "    0.060773815959692,\n", "    0.008533851243555546,\n", "    -0.0023885900154709816,\n", "    0.008428956381976604,\n", "    0.0018413548823446035,\n", "    0.03839820623397827,\n", "    -0.004159730393439531,\n", "    -0.03175255283713341,\n", "    0.04774205759167671,\n", "    0.03127715364098549,\n", "    0.01015099324285984,\n", "    0.02365068718791008,\n", "    0.049037862569093704,\n", "    0.025414789095520973,\n", "    0.005919378716498613,\n", "    -0.03253902122378349,\n", "    -0.020318394526839256,\n", "    0.05988635867834091,\n", "    0.01355766225606203,\n", "    0.04131853207945824,\n", "    -0.03089216724038124,\n", "    0.014581844210624695,\n", "    -0.004340263549238443,\n", "    0.08588723838329315,\n", "    0.027741355821490288,\n", "    -0.044811561703681946,\n", "    -0.06401520222425461,\n", "    -0.0936984121799469,\n", "    -0.023889774456620216,\n", "    1.0620419367306407e-30,\n", "    -0.0015085205668583512,\n", "    -0.06368714570999146,\n", "    0.005561797413975,\n", "    0.014763581566512585,\n", "    -0.016589557752013206,\n", "    -0.02097860351204872,\n", "    0.05195555090904236,\n", "    0.06079034507274628,\n", "    -0.0669013038277626,\n", "    0.09849873930215836,\n", "    0.08982204645872116,\n", "    0.035390641540288925,\n", "    0.02092173509299755,\n", "    -0.043076589703559875,\n", "    0.011049388907849789,\n", "    -0.07859054952859879,\n", "    -0.04791915416717529,\n", "    -0.07332592457532883,\n", "    -0.041806139051914215,\n", "    0.002225897740572691,\n", "    0.015609297901391983,\n", "    -0.07070238888263702,\n", "    -0.011421578004956245,\n", "    -0.06653468310832977,\n", "    -0.021364206448197365,\n", "    0.0341087244451046,\n", "    -0.005245328415185213,\n", "    -0.010838072746992111,\n", "    0.01506219245493412,\n", "    -0.0024049209896475077,\n", "    0.0694534182548523,\n", "    0.025501597672700882,\n", "    -0.16342489421367645,\n", "    0.002420071978121996,\n", "    -0.010825742967426777,\n", "    0.02875635214149952,\n", "    0.026042500510811806,\n", "    0.026156991720199585,\n", "    0.08345115184783936,\n", "    -0.12213212996721268,\n", "    -0.044817641377449036,\n", "    0.003690161509439349,\n", "    0.011096813715994358,\n", "    -0.040927205234766006,\n", "    0.016931036487221718,\n", "    0.00579177075996995,\n", "    0.0462116040289402,\n", "    -0.042809195816516876,\n", "    0.06203533336520195,\n", "    0.010682365857064724,\n", "    -0.07555490732192993,\n", "    -0.06728880107402802,\n", "    0.016786077991127968,\n", "    -0.07828478515148163,\n", "    -0.03272724896669388,\n", "    -0.04435906559228897,\n", "    0.08874183893203735,\n", "    -2.6360117772128433e-05,\n", "    -0.10432161390781403,\n", "    -0.035882577300071716,\n", "    0.04874756932258606,\n", "    -0.009768381714820862,\n", "    -0.09299672394990921,\n", "    0.06807490438222885,\n", "    -0.13082587718963623,\n", "    -0.08288871496915817,\n", "    -0.095990851521492,\n", "    -0.0707419142127037,\n", "    0.0431174673140049,\n", "    -0.10567788779735565,\n", "    -0.06638537347316742,\n", "    0.000670099921990186,\n", "    -0.0518481470644474,\n", "    -0.011118661612272263,\n", "    0.042346931993961334,\n", "    0.058716099709272385,\n", "    -0.0706477239727974,\n", "    -0.00583219900727272,\n", "    -0.017178848385810852,\n", "    -0.04509563371539116,\n", "    -0.03464510291814804,\n", "    -0.034867752343416214,\n", "    -0.07848349213600159,\n", "    0.011182291433215141,\n", "    -0.01651228405535221,\n", "    0.05304357409477234,\n", "    0.03540391847491264,\n", "    -0.01852884702384472,\n", "    0.058249492198228836,\n", "    0.04388921335339546,\n", "    -0.08991264551877975,\n", "    0.015492302365601063,\n", "    0.020721841603517532,\n", "    0.06777365505695343,\n", "    0.09231384843587875,\n", "    -2.5493605365145364e-33,\n", "    0.11756216734647751,\n", "    0.07083878666162491,\n", "    0.007033401634544134,\n", "    -0.023028986528515816,\n", "    0.06262994557619095,\n", "    -0.022728847339749336,\n", "    -0.001826483872719109,\n", "    -0.012331388890743256,\n", "    0.012480788864195347,\n", "    0.0521177276968956,\n", "    -0.04597879573702812,\n", "    0.01823180727660656,\n", "    0.05138251185417175,\n", "    -0.0241826344281435,\n", "    0.012502667494118214,\n", "    -0.04574580118060112,\n", "    0.01174137368798256,\n", "    0.013267483562231064,\n", "    -0.022194884717464447,\n", "    0.001560346339829266,\n", "    -0.0405118502676487,\n", "    0.031208328902721405,\n", "    -0.045158758759498596,\n", "    -0.10764965415000916,\n", "    0.00321552949026227,\n", "    -0.004810699727386236,\n", "    0.09738411754369736,\n", "    0.03462822362780571,\n", "    0.007651051040738821,\n", "    0.015601882711052895,\n", "    0.08177295327186584,\n", "    -0.010157143697142601,\n", "    0.06734579056501389,\n", "    -0.05192934349179268,\n", "    -0.044960808008909225,\n", "    0.05529945343732834,\n", "    -0.01954871229827404,\n", "    -0.037521012127399445,\n", "    -0.057922884821891785,\n", "    0.07742567360401154,\n", "    0.08210793882608414,\n", "    -0.01571657508611679,\n", "    0.0279042087495327,\n", "    -0.05282825976610184,\n", "    -0.003481428138911724,\n", "    -0.049973417073488235,\n", "    0.07504558563232422,\n", "    0.018159350380301476,\n", "    -0.011097822338342667,\n", "    0.04978879541158676,\n", "    -0.07169034332036972,\n", "    -0.012846962548792362,\n", "    -0.018024813383817673,\n", "    -0.08575183898210526,\n", "    0.10061586648225784,\n", "    0.04338996857404709,\n", "    -0.03546309098601341,\n", "    -0.009040134958922863,\n", "    -0.04351018741726875,\n", "    0.0029770836699754,\n", "    0.018910137936472893,\n", "    0.031697828322649,\n", "    -0.034243836998939514,\n", "    0.032739248126745224,\n", "    0.002267060801386833,\n", "    -0.007032542023807764,\n", "    -0.08874525129795074,\n", "    0.1372610330581665,\n", "    -0.017310617491602898,\n", "    0.025889677926898003,\n", "    -0.08086180686950684,\n", "    0.06767550110816956,\n", "    -0.006486565340310335,\n", "    -0.10002102702856064,\n", "    0.0019279334228485823,\n", "    -0.0703207403421402,\n", "    -0.01764548197388649,\n", "    0.02923884242773056,\n", "    0.021082760766148567,\n", "    0.011977225542068481,\n", "    0.016299238428473473,\n", "    0.011732163839042187,\n", "    -0.098166823387146,\n", "    0.049768123775720596,\n", "    0.005435686558485031,\n", "    0.1321045607328415,\n", "    0.04445530101656914,\n", "    -0.0498882457613945,\n", "    0.04907771944999695,\n", "    -0.0006577695021405816,\n", "    0.013559085316956043,\n", "    -0.013079159893095493,\n", "    0.09525947272777557,\n", "    0.0006249330472201109,\n", "    -0.04159910976886749,\n", "    -5.582328999848962e-33,\n", "    -0.10408525913953781,\n", "    -0.048025935888290405,\n", "    0.0375077910721302,\n", "    0.04910706356167793,\n", "    0.016617191955447197,\n", "    0.0634571835398674,\n", "    -0.06277075409889221,\n", "    0.08674203604459763,\n", "    -0.013013576157391071,\n", "    0.07521865516901016,\n", "    -0.048171479254961014,\n", "    0.07178705930709839,\n", "    -0.013302675448358059,\n", "    0.004861536435782909,\n", "    0.0023614915553480387,\n", "    0.01809016801416874,\n", "    0.05351995676755905,\n", "    0.06053472310304642,\n", "    -0.013793839141726494,\n", "    0.028294455260038376,\n", "    -0.018909931182861328,\n", "    0.013306024484336376,\n", "    -0.03594917058944702,\n", "    0.010508961975574493,\n", "    -0.03644927218556404,\n", "    -0.05899506434798241,\n", "    0.08486079424619675,\n", "    -0.01847236230969429,\n", "    -0.03317238390445709,\n", "    0.07162775099277496,\n", "    0.006177055183798075,\n", "    0.0676674172282219,\n", "    -0.034238580614328384,\n", "    -0.030061423778533936,\n", "    -0.04173043742775917,\n", "    -0.08579686284065247,\n", "    -0.007848815061151981,\n", "    0.007088419049978256,\n", "    0.07137580960988998,\n", "    0.06751947104930878,\n", "    -0.08099684119224548,\n", "    0.008751015178859234,\n", "    0.08509524166584015,\n", "    0.0668521374464035,\n", "    0.00432024197652936,\n", "    0.0732484981417656,\n", "    -0.1027846410870552,\n", "    -0.02107308804988861,\n", "    0.03377074748277664,\n", "    -0.05226410925388336,\n", "    -0.03137478232383728,\n", "    0.023883873596787453,\n", "    -0.054745741188526154,\n", "    -0.04062241315841675,\n", "    0.09677822887897491,\n", "    0.10927168279886246,\n", "    0.03545917198061943,\n", "    -0.025469807907938957,\n", "    -0.09432840347290039,\n", "    0.027133965864777565,\n", "    -0.08135542273521423,\n", "    -0.09554976969957352,\n", "    -0.012225285172462463,\n", "    -0.014137547463178635],\n", "   'text_vector': [-0.017636902630329132,\n", "    -0.05651654303073883,\n", "    0.06386150419712067,\n", "    -0.06699629873037338,\n", "    0.00392735144123435,\n", "    0.022594572976231575,\n", "    -0.03778490796685219,\n", "    -0.07157916575670242,\n", "    -0.03445133939385414,\n", "    0.05150710046291351,\n", "    0.03171079605817795,\n", "    -0.025567568838596344,\n", "    0.03925769031047821,\n", "    -0.07166711241006851,\n", "    0.09122028201818466,\n", "    -0.036673352122306824,\n", "    0.007321598008275032,\n", "    -0.024228041991591454,\n", "    0.017352860420942307,\n", "    -0.01525530219078064,\n", "    -0.07071347534656525,\n", "    0.0029026565607637167,\n", "    -0.06350894272327423,\n", "    -0.001522350125014782,\n", "    -0.04834355413913727,\n", "    -0.0552159883081913,\n", "    0.026479987427592278,\n", "    -0.04927630349993706,\n", "    -0.04029929265379906,\n", "    0.04488280788064003,\n", "    -0.01149942446500063,\n", "    -0.059342045336961746,\n", "    -0.013081338256597519,\n", "    0.04824689403176308,\n", "    0.09772410988807678,\n", "    0.016414150595664978,\n", "    -0.009122720919549465,\n", "    -0.04745808616280556,\n", "    -0.05118216201663017,\n", "    -0.01858704909682274,\n", "    0.04520922526717186,\n", "    -0.06715095788240433,\n", "    -0.053327836096286774,\n", "    -0.023811278864741325,\n", "    0.11985138058662415,\n", "    0.04104167968034744,\n", "    0.01719396561384201,\n", "    -0.08914537727832794,\n", "    0.05197467282414436,\n", "    -0.06162377446889877,\n", "    -0.02488783560693264,\n", "    -0.036774322390556335,\n", "    -0.03650594502687454,\n", "    -0.04087386280298233,\n", "    -0.05761502683162689,\n", "    -0.024055378511548042,\n", "    -0.034900516271591187,\n", "    0.04455679655075073,\n", "    0.05191593989729881,\n", "    -0.02690301090478897,\n", "    -0.08879218995571136,\n", "    -0.03547156602144241,\n", "    0.02345198579132557,\n", "    0.013037904165685177,\n", "    -0.00813802145421505,\n", "    0.10049940645694733,\n", "    0.01628049463033676,\n", "    0.021371882408857346,\n", "    0.04230233654379845,\n", "    -0.0016221172409132123,\n", "    -0.11361229419708252,\n", "    -0.05494548752903938,\n", "    -0.06709646433591843,\n", "    0.00036517035914584994,\n", "    -0.04918668791651726,\n", "    -0.04652474820613861,\n", "    0.03286408260464668,\n", "    0.07833931595087051,\n", "    0.04292352497577667,\n", "    0.0020900724921375513,\n", "    -0.04584655538201332,\n", "    0.07790715247392654,\n", "    -0.036012791097164154,\n", "    0.10709579288959503,\n", "    -0.03218469396233559,\n", "    -0.08206721395254135,\n", "    0.05314283072948456,\n", "    0.05564309284090996,\n", "    0.02535678632557392,\n", "    -0.013175198808312416,\n", "    0.07036571949720383,\n", "    0.02177329547703266,\n", "    0.0031334853265434504,\n", "    0.040219444781541824,\n", "    0.012646466493606567,\n", "    -0.007720381952822208,\n", "    0.00020433703321032226,\n", "    -0.07080069929361343,\n", "    0.010253410786390305,\n", "    -0.019464045763015747,\n", "    -0.042576082050800323,\n", "    -0.030032895505428314,\n", "    0.019602524116635323,\n", "    -0.0348205603659153,\n", "    0.0073133655823767185,\n", "    0.0028180035296827555,\n", "    0.013944817706942558,\n", "    -0.024280354380607605,\n", "    0.0949162021279335,\n", "    0.006630350835621357,\n", "    0.015053329057991505,\n", "    0.008877726271748543,\n", "    -0.02595776878297329,\n", "    -0.01438807137310505,\n", "    0.046020593494176865,\n", "    0.07885927706956863,\n", "    -0.08898584544658661,\n", "    -0.0668984055519104,\n", "    0.052717551589012146,\n", "    0.04898612201213837,\n", "    0.02050887607038021,\n", "    -0.01613357663154602,\n", "    0.03360475227236748,\n", "    -0.013035191223025322,\n", "    -0.10353334993124008,\n", "    0.029446400701999664,\n", "    -0.06459269672632217,\n", "    -2.6246209819255976e-31,\n", "    0.03195072337985039,\n", "    0.016344785690307617,\n", "    0.01535741239786148,\n", "    0.025689586997032166,\n", "    0.004886148031800985,\n", "    0.03050771914422512,\n", "    -0.030508147552609444,\n", "    -0.08836553245782852,\n", "    -0.10890670120716095,\n", "    -0.02528286911547184,\n", "    -0.018249697983264923,\n", "    0.04718678444623947,\n", "    -0.0447249598801136,\n", "    0.06185101345181465,\n", "    0.00827407743781805,\n", "    -0.0012524058111011982,\n", "    -0.04250967130064964,\n", "    -0.02784108743071556,\n", "    0.08346126228570938,\n", "    0.004772323649376631,\n", "    -0.04033610224723816,\n", "    -0.025023197755217552,\n", "    0.0689263716340065,\n", "    -0.1062144860625267,\n", "    0.06251118332147598,\n", "    -0.06076355278491974,\n", "    -0.02036569081246853,\n", "    -0.045781705528497696,\n", "    0.032258566468954086,\n", "    -0.009475608356297016,\n", "    0.04142741858959198,\n", "    -0.015459991991519928,\n", "    -0.0024262110237032175,\n", "    0.008859327994287014,\n", "    -0.07299638539552689,\n", "    -0.052796509116888046,\n", "    -0.12983615696430206,\n", "    -0.04083523899316788,\n", "    0.058333564549684525,\n", "    0.013975423760712147,\n", "    0.0894114151597023,\n", "    0.054151780903339386,\n", "    0.05808074772357941,\n", "    -0.01529337465763092,\n", "    0.03409314528107643,\n", "    -0.021804146468639374,\n", "    0.07483716309070587,\n", "    -0.026804903522133827,\n", "    0.1474069505929947,\n", "    0.05117880925536156,\n", "    -0.02127603068947792,\n", "    -0.036021336913108826,\n", "    -0.007265464402735233,\n", "    0.01927240937948227,\n", "    0.016821905970573425,\n", "    0.01824975199997425,\n", "    0.04696795344352722,\n", "    -0.010639344342052937,\n", "    -0.022351836785674095,\n", "    -0.01599734090268612,\n", "    -0.05352224037051201,\n", "    -0.02619694545865059,\n", "    -0.014713810756802559,\n", "    0.07307592034339905,\n", "    0.02327580563724041,\n", "    -0.041745420545339584,\n", "    -0.10966704785823822,\n", "    0.08659513294696808,\n", "    0.002839148510247469,\n", "    -0.04056759551167488,\n", "    -0.029308490455150604,\n", "    -0.041926927864551544,\n", "    0.012509430758655071,\n", "    -0.03310297057032585,\n", "    -0.006441741716116667,\n", "    -0.04631487652659416,\n", "    -0.04957545921206474,\n", "    -0.021648935973644257,\n", "    -0.09345927089452744,\n", "    0.09217899292707443,\n", "    -0.02342677302658558,\n", "    0.07907307893037796,\n", "    0.00476238364353776,\n", "    0.07099020481109619,\n", "    -0.04118504747748375,\n", "    0.03689248114824295,\n", "    0.06295233219861984,\n", "    0.07921665161848068,\n", "    0.031757816672325134,\n", "    0.013373536057770252,\n", "    -0.02290700189769268,\n", "    0.0004963180981576443,\n", "    0.014314551837742329,\n", "    0.04574849084019661,\n", "    -0.03684704005718231,\n", "    -4.2039755481434416e-33,\n", "    -0.04908885061740875,\n", "    -0.034337446093559265,\n", "    0.007846640422940254,\n", "    0.022209538146853447,\n", "    0.12664340436458588,\n", "    -0.07027482241392136,\n", "    0.07221284508705139,\n", "    -0.07461751997470856,\n", "    0.09366396069526672,\n", "    0.06092352420091629,\n", "    -0.07482075691223145,\n", "    -0.037454959005117416,\n", "    0.09532977640628815,\n", "    0.011636377312242985,\n", "    -0.0665251761674881,\n", "    0.01953836716711521,\n", "    -0.03647070378065109,\n", "    -0.06619682163000107,\n", "    0.0018919350113719702,\n", "    0.0067025660537183285,\n", "    -0.0978415235877037,\n", "    -0.0004161992692388594,\n", "    0.004983534105122089,\n", "    -0.030932456254959106,\n", "    -0.014355751685798168,\n", "    -0.03932739794254303,\n", "    -0.022859184071421623,\n", "    0.003334545996040106,\n", "    0.02016238123178482,\n", "    -0.03453163802623749,\n", "    -0.011721145361661911,\n", "    0.09239674359560013,\n", "    0.03748727962374687,\n", "    -0.01659524254500866,\n", "    0.006461583077907562,\n", "    -0.06310213357210159,\n", "    -0.021604495123028755,\n", "    -0.047295942902565,\n", "    0.07027147710323334,\n", "    -0.05037391185760498,\n", "    0.11630216240882874,\n", "    -0.08846806734800339,\n", "    -0.03910280391573906,\n", "    -0.12435773015022278,\n", "    -0.02562074363231659,\n", "    0.03622765094041824,\n", "    0.08153355866670609,\n", "    0.09840455651283264,\n", "    -0.05062660202383995,\n", "    -0.006760348565876484,\n", "    -0.003075104206800461,\n", "    -0.0028978115878999233,\n", "    -0.009636479429900646,\n", "    0.04826347157359123,\n", "    0.04992404580116272,\n", "    0.017966018989682198,\n", "    0.053797438740730286,\n", "    0.09463490545749664,\n", "    -0.028319336473941803,\n", "    0.01828477531671524,\n", "    -0.046435315161943436,\n", "    0.04113779589533806,\n", "    0.11794808506965637,\n", "    -0.008012039586901665,\n", "    -0.07211960107088089,\n", "    -0.0028342166915535927,\n", "    -0.004905948415398598,\n", "    -0.03116912767291069,\n", "    -0.02604207955300808,\n", "    0.011174659244716167,\n", "    -0.08081433176994324,\n", "    -0.0703987255692482,\n", "    0.04372463375329971,\n", "    -0.06801003217697144,\n", "    -0.0038237080443650484,\n", "    0.03807580843567848,\n", "    -0.05538611486554146,\n", "    -0.0049945819191634655,\n", "    0.02062857151031494,\n", "    -0.05823077633976936,\n", "    -0.004413506481796503,\n", "    0.07485368847846985,\n", "    0.011018026620149612,\n", "    0.010330726392567158,\n", "    0.004124350380152464,\n", "    0.07913597673177719,\n", "    0.004712388850748539,\n", "    0.03543085232377052,\n", "    0.029742995277047157,\n", "    0.05126918479800224,\n", "    -0.05266466364264488,\n", "    -0.0445280596613884,\n", "    0.010023800656199455,\n", "    0.06904176622629166,\n", "    -0.0043924590572714806,\n", "    -2.2381298563641118e-33,\n", "    0.0998104140162468,\n", "    0.06464364379644394,\n", "    0.046301309019327164,\n", "    0.04898875206708908,\n", "    -0.050991762429475784,\n", "    0.0897141844034195,\n", "    0.05886395275592804,\n", "    0.07478436827659607,\n", "    0.022512556985020638,\n", "    0.06088706851005554,\n", "    0.016276683658361435,\n", "    -0.09379619359970093,\n", "    -0.010297410190105438,\n", "    0.04860455542802811,\n", "    -0.030654365196824074,\n", "    0.07607027888298035,\n", "    -0.009599816985428333,\n", "    0.054162316024303436,\n", "    0.013827378861606121,\n", "    -0.02459038235247135,\n", "    -0.054295387119054794,\n", "    0.0240873321890831,\n", "    -0.007981333881616592,\n", "    0.016131730750203133,\n", "    0.006178536452353001,\n", "    0.01243808213621378,\n", "    0.0719255730509758,\n", "    0.0011908384039998055,\n", "    -0.10761144012212753,\n", "    -0.05661056190729141,\n", "    0.0013768498320132494,\n", "    -0.03667151555418968,\n", "    -0.0781266838312149,\n", "    -0.07574307173490524,\n", "    0.06733590364456177,\n", "    -0.07350298017263412,\n", "    -0.09375238418579102,\n", "    -0.01597990095615387,\n", "    0.09492883086204529,\n", "    0.05599977821111679,\n", "    -0.03200628235936165,\n", "    0.06943698227405548,\n", "    0.0593591146171093,\n", "    -0.04493232071399689,\n", "    -0.010497894138097763,\n", "    -0.003934232983738184,\n", "    0.002859616419300437,\n", "    0.0018790897447615862,\n", "    -0.030500082299113274,\n", "    0.10024704039096832,\n", "    -0.031179966405034065,\n", "    -0.050720326602458954,\n", "    -0.029831184074282646,\n", "    0.017743872478604317,\n", "    0.07744907587766647,\n", "    0.06119122356176376,\n", "    -0.0022419258020818233,\n", "    -0.002589063486084342,\n", "    -0.07097729295492172,\n", "    0.006674737669527531,\n", "    -0.0043666972778737545,\n", "    0.022129837423563004,\n", "    0.030262809246778488,\n", "    -0.05494750291109085],\n", "   'question_text_vector': [0.009777250699698925,\n", "    -0.030579520389437675,\n", "    0.051414765417575836,\n", "    -0.048747606575489044,\n", "    0.009609597735106945,\n", "    0.018407201394438744,\n", "    -0.0210348442196846,\n", "    -0.08516397327184677,\n", "    -0.03166235238313675,\n", "    0.05175965651869774,\n", "    0.036135319620370865,\n", "    -0.013332987204194069,\n", "    0.05132441222667694,\n", "    -0.057813454419374466,\n", "    0.08410763740539551,\n", "    -0.06113869696855545,\n", "    -0.013435795903205872,\n", "    -0.03681454807519913,\n", "    0.026083175092935562,\n", "    -0.019738981500267982,\n", "    -0.114227794110775,\n", "    0.005927842576056719,\n", "    -0.055482205003499985,\n", "    0.018564768135547638,\n", "    -0.035555433481931686,\n", "    -0.04475342854857445,\n", "    0.014581668190658092,\n", "    -0.04301957041025162,\n", "    -0.03864341229200363,\n", "    0.03170900046825409,\n", "    -0.00244521489366889,\n", "    -0.0350288562476635,\n", "    -0.006621825508773327,\n", "    0.0541372187435627,\n", "    0.09570817649364471,\n", "    0.0539371594786644,\n", "    -0.018086113035678864,\n", "    -0.0599321573972702,\n", "    -0.038253266364336014,\n", "    -0.003611331805586815,\n", "    0.03578851744532585,\n", "    -0.0649455338716507,\n", "    -0.05023743212223053,\n", "    -0.018878746777772903,\n", "    0.14200130105018616,\n", "    0.05313826724886894,\n", "    0.021520351991057396,\n", "    -0.13118945062160492,\n", "    0.06466786563396454,\n", "    -0.0506744422018528,\n", "    -0.02560489811003208,\n", "    -0.06479409337043762,\n", "    -0.04822501540184021,\n", "    -0.05015398561954498,\n", "    -0.08079936355352402,\n", "    0.013899148441851139,\n", "    -0.02821284346282482,\n", "    0.03839704766869545,\n", "    0.035177990794181824,\n", "    -0.02612333931028843,\n", "    -0.09305471181869507,\n", "    -0.04796261712908745,\n", "    0.002773317275568843,\n", "    0.017087524756789207,\n", "    -0.009070942178368568,\n", "    0.09233621507883072,\n", "    0.008030174300074577,\n", "    0.054553840309381485,\n", "    0.07678740471601486,\n", "    -0.017012862488627434,\n", "    -0.0886114314198494,\n", "    -0.033618662506341934,\n", "    -0.043155744671821594,\n", "    0.021036699414253235,\n", "    0.002274865750223398,\n", "    -0.05282949283719063,\n", "    0.014910133555531502,\n", "    0.06083719804883003,\n", "    0.05602915585041046,\n", "    0.005297880154103041,\n", "    -0.021031558513641357,\n", "    0.06670951843261719,\n", "    -0.035609494894742966,\n", "    0.10597500205039978,\n", "    -0.031654391437768936,\n", "    -0.09355571866035461,\n", "    0.08111344277858734,\n", "    0.049974266439676285,\n", "    0.01980082131922245,\n", "    -0.008694333024322987,\n", "    0.06487850099802017,\n", "    -0.0044027771800756454,\n", "    -0.006831880193203688,\n", "    0.04912867769598961,\n", "    -0.009009633213281631,\n", "    -0.012258431874215603,\n", "    -0.004493008833378553,\n", "    -0.07704640924930573,\n", "    0.017419682815670967,\n", "    -0.010657145641744137,\n", "    -0.055471908301115036,\n", "    -0.019285915419459343,\n", "    -0.02070542238652706,\n", "    -0.03822808340191841,\n", "    0.008916529826819897,\n", "    0.0004298229468986392,\n", "    0.030788946896791458,\n", "    -0.003433331847190857,\n", "    0.09273134917020798,\n", "    -0.0044536590576171875,\n", "    -0.00169086002279073,\n", "    0.018002618104219437,\n", "    -0.029034985229372978,\n", "    -0.02404921129345894,\n", "    0.03742729499936104,\n", "    0.07947927713394165,\n", "    -0.07624002546072006,\n", "    -0.03303689509630203,\n", "    0.024678170680999756,\n", "    0.05166531726717949,\n", "    0.003553178394213319,\n", "    -0.0065020108595490456,\n", "    0.0459606796503067,\n", "    -0.02100529335439205,\n", "    -0.11589270085096359,\n", "    -0.0029195339884608984,\n", "    -0.05635084584355354,\n", "    -3.0677106470302e-31,\n", "    0.04036198556423187,\n", "    -0.013885194435715675,\n", "    0.016908027231693268,\n", "    0.01233726553618908,\n", "    0.039893776178359985,\n", "    0.02325517125427723,\n", "    -0.027007712051272392,\n", "    -0.06745120882987976,\n", "    -0.11274833232164383,\n", "    -0.007352957036346197,\n", "    0.005053621716797352,\n", "    0.04178377240896225,\n", "    -0.022206775844097137,\n", "    0.05093979090452194,\n", "    0.0013876792509108782,\n", "    -0.005173971876502037,\n", "    -0.06984150409698486,\n", "    -0.021861348301172256,\n", "    0.058558814227581024,\n", "    0.005799436010420322,\n", "    -0.03367666155099869,\n", "    -0.020457150414586067,\n", "    0.034570593386888504,\n", "    -0.11949820816516876,\n", "    0.06398490816354752,\n", "    -0.049841947853565216,\n", "    -0.0215343926101923,\n", "    -0.03694393113255501,\n", "    0.03995455801486969,\n", "    -0.00027897756081074476,\n", "    0.05593537911772728,\n", "    0.00149780442006886,\n", "    -0.051203589886426926,\n", "    0.01881890743970871,\n", "    -0.07355672866106033,\n", "    -0.02681795135140419,\n", "    -0.10434540361166,\n", "    -0.034735895693302155,\n", "    0.07299942523241043,\n", "    -0.00968911312520504,\n", "    0.07223784178495407,\n", "    0.039862241595983505,\n", "    0.06743092834949493,\n", "    -0.02232067659497261,\n", "    0.0025730940978974104,\n", "    -0.0017319974722340703,\n", "    0.08945093303918839,\n", "    -0.04343169927597046,\n", "    0.1418280452489853,\n", "    0.0436788946390152,\n", "    -0.04476934298872948,\n", "    -0.03885060176253319,\n", "    -0.004292600322514772,\n", "    0.007654756773263216,\n", "    0.015685049816966057,\n", "    0.009919493459165096,\n", "    0.04805198684334755,\n", "    -0.007577433716505766,\n", "    -0.03361597657203674,\n", "    -0.015665002167224884,\n", "    -0.05031168460845947,\n", "    -0.020468056201934814,\n", "    -0.033810000866651535,\n", "    0.07606729865074158,\n", "    -0.010860725305974483,\n", "    -0.04509970545768738,\n", "    -0.10837354511022568,\n", "    0.05869363248348236,\n", "    0.03486587852239609,\n", "    -0.04730771854519844,\n", "    -0.05469634756445885,\n", "    -0.033407289534807205,\n", "    -0.0151603352278471,\n", "    -0.046178124845027924,\n", "    -0.002844004426151514,\n", "    -0.05520493537187576,\n", "    -0.048202987760305405,\n", "    -0.032686568796634674,\n", "    -0.07954360544681549,\n", "    0.09454278647899628,\n", "    -0.02797035314142704,\n", "    0.06629425287246704,\n", "    -0.006198367569595575,\n", "    0.07422911375761032,\n", "    -0.06000860780477524,\n", "    0.0638452097773552,\n", "    0.06968913972377777,\n", "    0.059825100004673004,\n", "    0.05697992071509361,\n", "    0.03415774554014206,\n", "    -0.04225104674696922,\n", "    -0.006892077624797821,\n", "    0.01024880725890398,\n", "    0.044210463762283325,\n", "    -0.034398432821035385,\n", "    -4.680531978226336e-33,\n", "    0.0015612478600814939,\n", "    -0.024167479947209358,\n", "    -0.007936878129839897,\n", "    0.024182651191949844,\n", "    0.13018137216567993,\n", "    -0.07892027497291565,\n", "    0.0672016441822052,\n", "    -0.043248530477285385,\n", "    0.07463931292295456,\n", "    0.07691407948732376,\n", "    -0.051435474306344986,\n", "    -0.03272759169340134,\n", "    0.07905911654233932,\n", "    0.009467394091188908,\n", "    -0.03837426006793976,\n", "    0.0018967107171192765,\n", "    -0.01977594941854477,\n", "    -0.05909758061170578,\n", "    -0.018737642094492912,\n", "    0.0045866831205785275,\n", "    -0.11335835605859756,\n", "    0.006328375078737736,\n", "    0.009624294936656952,\n", "    -0.05500265955924988,\n", "    -0.0051901498809456825,\n", "    -0.036706846207380295,\n", "    -0.010865624062716961,\n", "    0.02325616404414177,\n", "    0.03549335151910782,\n", "    -0.03941798955202103,\n", "    0.016484422609210014,\n", "    0.07169578224420547,\n", "    0.028410812839865685,\n", "    -0.01909409835934639,\n", "    -0.007672361563891172,\n", "    -0.04443764686584473,\n", "    -0.031686678528785706,\n", "    -0.047426965087652206,\n", "    0.030824381858110428,\n", "    -0.031887032091617584,\n", "    0.11481599509716034,\n", "    -0.07916398346424103,\n", "    -0.0414154976606369,\n", "    -0.11767178773880005,\n", "    -0.008909487165510654,\n", "    0.03886762633919716,\n", "    0.08635972440242767,\n", "    0.09479766339063644,\n", "    -0.0444168746471405,\n", "    0.00042719111661426723,\n", "    -0.019544530659914017,\n", "    -0.025336017832159996,\n", "    -0.009971825405955315,\n", "    0.03926745429635048,\n", "    0.0750875398516655,\n", "    0.026175837963819504,\n", "    0.044971827417612076,\n", "    0.08738573640584946,\n", "    -0.051641855388879776,\n", "    0.026044420897960663,\n", "    -0.024431193247437477,\n", "    0.029216645285487175,\n", "    0.10854382067918777,\n", "    0.0013891014968976378,\n", "    -0.06131605803966522,\n", "    0.004386112559586763,\n", "    -0.011904802173376083,\n", "    -0.006819830276072025,\n", "    -0.009695691987872124,\n", "    0.00951757188886404,\n", "    -0.08276082575321198,\n", "    -0.0510382317006588,\n", "    0.030401231721043587,\n", "    -0.0985105037689209,\n", "    0.007126540411263704,\n", "    0.02946445904672146,\n", "    -0.060289204120635986,\n", "    0.031217200681567192,\n", "    0.009256969206035137,\n", "    -0.0488312654197216,\n", "    -0.00929114781320095,\n", "    0.06995200365781784,\n", "    -0.019456107169389725,\n", "    0.02229483425617218,\n", "    -0.0025861207395792007,\n", "    0.09735851734876633,\n", "    0.018907323479652405,\n", "    0.0156254880130291,\n", "    0.025232700631022453,\n", "    0.06971941888332367,\n", "    -0.029010869562625885,\n", "    -0.05634213984012604,\n", "    0.029738718643784523,\n", "    0.050166938453912735,\n", "    -0.0001427104725735262,\n", "    -2.8360292622045292e-33,\n", "    0.05973668023943901,\n", "    0.07639510184526443,\n", "    0.047121815383434296,\n", "    0.062479373067617416,\n", "    -0.049072980880737305,\n", "    0.100919209420681,\n", "    0.04502393677830696,\n", "    0.08587686717510223,\n", "    0.010670661926269531,\n", "    0.09477643668651581,\n", "    -0.008078533224761486,\n", "    -0.08369172364473343,\n", "    -0.02253710851073265,\n", "    0.042008690536022186,\n", "    -0.0192964356392622,\n", "    0.06694978475570679,\n", "    0.0034310317132622004,\n", "    0.08934282511472702,\n", "    0.0031329484190791845,\n", "    -0.015695195645093918,\n", "    -0.051558636128902435,\n", "    0.019760362803936005,\n", "    0.004497441463172436,\n", "    0.02956502139568329,\n", "    -0.022250285372138023,\n", "    0.019666731357574463,\n", "    0.088654525578022,\n", "    0.010244084522128105,\n", "    -0.1242285668849945,\n", "    -0.03960990905761719,\n", "    -0.009839322417974472,\n", "    -0.03399175778031349,\n", "    -0.09434384852647781,\n", "    -0.05541779845952988,\n", "    0.047621939331293106,\n", "    -0.10239806771278381,\n", "    -0.08556754887104034,\n", "    -0.016003845259547234,\n", "    0.08825307339429855,\n", "    0.0785621628165245,\n", "    -0.0717817172408104,\n", "    0.0392136350274086,\n", "    0.06563880294561386,\n", "    -0.014479194767773151,\n", "    -0.0020616757683455944,\n", "    0.008478494361042976,\n", "    0.003999881446361542,\n", "    0.010648055002093315,\n", "    -0.018327178433537483,\n", "    0.07865487784147263,\n", "    -0.032420072704553604,\n", "    -0.04309074208140373,\n", "    -0.05139734223484993,\n", "    -0.006122618913650513,\n", "    0.09780846536159515,\n", "    0.0782594308257103,\n", "    0.023998573422431946,\n", "    -0.022741101682186127,\n", "    -0.09404194355010986,\n", "    0.011243034154176712,\n", "    -0.032959096133708954,\n", "    -0.03077832981944084,\n", "    0.007800901774317026,\n", "    -0.05766924470663071]}},\n", " {'_index': 'course-questions',\n", "  '_id': 'JiPvR5EBO7bpWV6zkXVu',\n", "  '_score': 21.325657,\n", "  '_source': {'text': 'Yes, the slack channel remains open and you can ask questions there. But always sDocker containers exit code w search the channel first and second, check the FAQ (this document), most likely all your questions are already answered here.\\nYou can also tag the bot @ZoomcampQABot to help you conduct the search, but don’t rely on its answers 100%, it is pretty good though.',\n", "   'section': 'General course-related questions',\n", "   'question': 'Course - Can I get support if I take the course in the self-paced mode?',\n", "   'course': 'data-engineering-zoomcamp',\n", "   'id': 'eb56ae98',\n", "   'question_vector': [0.011246051639318466,\n", "    -0.06579667329788208,\n", "    -0.03573142737150192,\n", "    0.032929327338933945,\n", "    -0.0017230658559128642,\n", "    0.05562397465109825,\n", "    -0.066682830452919,\n", "    -0.061834488064050674,\n", "    -0.08855446428060532,\n", "    -0.037421129643917084,\n", "    -0.011051102541387081,\n", "    0.08929185569286346,\n", "    -0.0013798653380945325,\n", "    0.014406107366085052,\n", "    0.07046189904212952,\n", "    -0.08452175557613373,\n", "    0.04384994879364967,\n", "    0.003738317871466279,\n", "    0.0032299093436449766,\n", "    0.0784306600689888,\n", "    -0.07173117995262146,\n", "    -0.0003634604508988559,\n", "    -0.005943913944065571,\n", "    0.03215339779853821,\n", "    -0.0047195665538311005,\n", "    -0.06775344908237457,\n", "    -0.005250426009297371,\n", "    -0.00038966358988545835,\n", "    0.0019353782990947366,\n", "    -0.0023932773619890213,\n", "    0.03129374235868454,\n", "    -0.029684320092201233,\n", "    0.029833341017365456,\n", "    -0.01742413640022278,\n", "    -0.049185723066329956,\n", "    0.050805844366550446,\n", "    -0.011798000894486904,\n", "    0.004022185690701008,\n", "    -0.08283557742834091,\n", "    0.059672530740499496,\n", "    -0.0073877680115401745,\n", "    0.0262561347335577,\n", "    0.020679833367466927,\n", "    0.018175773322582245,\n", "    0.09589944779872894,\n", "    0.023115620017051697,\n", "    -0.036028217524290085,\n", "    -0.09889127314090729,\n", "    -0.005104428622871637,\n", "    0.023865321651101112,\n", "    -0.05407628044486046,\n", "    -0.020987050607800484,\n", "    0.011217000894248486,\n", "    -0.00014306552475318313,\n", "    -0.0157834030687809,\n", "    0.0851467177271843,\n", "    -0.019899964332580566,\n", "    0.08860165625810623,\n", "    -0.09015611559152603,\n", "    0.09056548029184341,\n", "    -0.00485716387629509,\n", "    -0.014743116684257984,\n", "    -0.10110656172037125,\n", "    -0.027864057570695877,\n", "    0.013388417661190033,\n", "    0.09724032133817673,\n", "    0.0006137272575870156,\n", "    0.08719640225172043,\n", "    0.093931183218956,\n", "    0.013312648981809616,\n", "    -0.006884588859975338,\n", "    0.011013886891305447,\n", "    0.06316862255334854,\n", "    0.001980859786272049,\n", "    0.07917032390832901,\n", "    0.037923913449048996,\n", "    -0.03720293939113617,\n", "    -0.06381302326917648,\n", "    0.16206999123096466,\n", "    0.021277377381920815,\n", "    -0.013854599557816982,\n", "    0.03649622201919556,\n", "    0.026263125240802765,\n", "    -0.02813008800148964,\n", "    -0.010372680611908436,\n", "    -0.08517789095640182,\n", "    0.09032618999481201,\n", "    0.05174817144870758,\n", "    0.007978980429470539,\n", "    0.04263446480035782,\n", "    0.005030836910009384,\n", "    -0.01605622097849846,\n", "    -0.019764700904488564,\n", "    -0.01972239837050438,\n", "    -0.00708497129380703,\n", "    0.06238787993788719,\n", "    -0.019575702026486397,\n", "    -0.04673253744840622,\n", "    -0.062137946486473083,\n", "    -0.013169613666832447,\n", "    -0.05880020186305046,\n", "    0.00319341360591352,\n", "    -0.008566326461732388,\n", "    0.06171795353293419,\n", "    -0.060056500136852264,\n", "    0.000788628589361906,\n", "    0.062339361757040024,\n", "    0.06178874149918556,\n", "    0.026473121717572212,\n", "    0.04229595139622688,\n", "    0.015351373702287674,\n", "    0.02234036847949028,\n", "    0.03435039892792702,\n", "    -0.043342821300029755,\n", "    -0.02427913248538971,\n", "    0.03580121323466301,\n", "    -0.062162451446056366,\n", "    0.06373675912618637,\n", "    0.05720124393701553,\n", "    0.009892824105918407,\n", "    0.04573250189423561,\n", "    -0.019662870094180107,\n", "    0.04768920689821243,\n", "    -0.06175145134329796,\n", "    0.05381215363740921,\n", "    -0.12443450093269348,\n", "    0.0016665314324200153,\n", "    1.2622898444214671e-30,\n", "    0.023941412568092346,\n", "    -0.06989891082048416,\n", "    0.01458943635225296,\n", "    0.010277674533426762,\n", "    -0.0036717578768730164,\n", "    -0.021372992545366287,\n", "    0.011662213131785393,\n", "    0.03399643674492836,\n", "    0.0022838676813989878,\n", "    -0.03836983069777489,\n", "    0.009179857559502125,\n", "    0.030048102140426636,\n", "    0.04390554130077362,\n", "    -0.017213521525263786,\n", "    0.03165126591920853,\n", "    -0.022374501451849937,\n", "    0.008101562969386578,\n", "    -0.003393696853891015,\n", "    0.0010126513661816716,\n", "    -0.061602070927619934,\n", "    0.06524815410375595,\n", "    -0.04380204528570175,\n", "    -0.02237500250339508,\n", "    -0.08227397501468658,\n", "    0.0435948371887207,\n", "    0.012380462139844894,\n", "    0.06346148997545242,\n", "    0.02037007547914982,\n", "    -0.003986277151852846,\n", "    0.032248739153146744,\n", "    0.023685272783041,\n", "    0.04296875,\n", "    -0.1677599847316742,\n", "    -0.042489830404520035,\n", "    0.029314834624528885,\n", "    0.029911568388342857,\n", "    -0.007237347308546305,\n", "    0.052168700844049454,\n", "    0.04059506952762604,\n", "    -0.046976979821920395,\n", "    -0.0711047425866127,\n", "    0.0010469709523022175,\n", "    0.059804946184158325,\n", "    -0.02936028689146042,\n", "    -0.06957637518644333,\n", "    0.01614868827164173,\n", "    0.11896917223930359,\n", "    -0.03437706455588341,\n", "    -0.10080759972333908,\n", "    -0.014068451710045338,\n", "    -0.1026124581694603,\n", "    -0.021447939798235893,\n", "    -0.020567500963807106,\n", "    -0.07844936102628708,\n", "    0.02554137073457241,\n", "    -0.00727575458586216,\n", "    0.029129305854439735,\n", "    0.08776222169399261,\n", "    -0.005673709791153669,\n", "    0.033187076449394226,\n", "    0.026329342275857925,\n", "    -0.04334273934364319,\n", "    -0.042275696992874146,\n", "    0.021771062165498734,\n", "    -0.08530581742525101,\n", "    -0.014543058350682259,\n", "    -0.0901966392993927,\n", "    -0.11509327590465546,\n", "    0.06067013740539551,\n", "    0.017735641449689865,\n", "    -0.08679863810539246,\n", "    0.01584702916443348,\n", "    -0.060560278594493866,\n", "    -0.011023435741662979,\n", "    0.041922152042388916,\n", "    0.007682382129132748,\n", "    -0.040228068828582764,\n", "    -0.025954781100153923,\n", "    -0.004085309337824583,\n", "    -0.049799609929323196,\n", "    -0.004391497001051903,\n", "    -0.05149572342634201,\n", "    0.021307267248630524,\n", "    0.025236506015062332,\n", "    0.04707694798707962,\n", "    0.04285997524857521,\n", "    -0.018481796607375145,\n", "    -0.008658534847199917,\n", "    0.09355107694864273,\n", "    0.06980884075164795,\n", "    -0.042986366897821426,\n", "    -0.04416242614388466,\n", "    -0.05028461664915085,\n", "    0.052044279873371124,\n", "    0.019064053893089294,\n", "    -2.272682961793556e-33,\n", "    0.02538723684847355,\n", "    0.013632149435579777,\n", "    -0.0028321153949946165,\n", "    -0.05851258710026741,\n", "    0.04889979958534241,\n", "    0.010718822479248047,\n", "    0.011143350042402744,\n", "    0.030352678149938583,\n", "    0.023423107340931892,\n", "    0.08227741718292236,\n", "    -0.046519484370946884,\n", "    -0.045518066734075546,\n", "    -0.010576177388429642,\n", "    0.029336685314774513,\n", "    -0.04452523961663246,\n", "    -0.07208202034235,\n", "    -0.13115373253822327,\n", "    -0.02859659306704998,\n", "    0.01690899021923542,\n", "    0.017277516424655914,\n", "    -0.04613190144300461,\n", "    0.0005889996537007391,\n", "    0.07147545367479324,\n", "    -0.04715000092983246,\n", "    0.09073961526155472,\n", "    0.04227583855390549,\n", "    -0.01488562859594822,\n", "    0.0461198091506958,\n", "    0.019653495401144028,\n", "    0.027637813240289688,\n", "    0.06655001640319824,\n", "    -0.08054196834564209,\n", "    -0.08338531106710434,\n", "    -0.12300712615251541,\n", "    0.012373028323054314,\n", "    0.09764814376831055,\n", "    -0.07067302614450455,\n", "    0.017451206222176552,\n", "    -0.07477197796106339,\n", "    0.06268355995416641,\n", "    0.05800073966383934,\n", "    -0.01644808240234852,\n", "    -0.015069441869854927,\n", "    -0.0699518471956253,\n", "    0.053508445620536804,\n", "    -0.03254101052880287,\n", "    0.07887159287929535,\n", "    -0.026822661980986595,\n", "    -0.04511253163218498,\n", "    0.05834568291902542,\n", "    -0.06037547439336777,\n", "    -0.012347699142992496,\n", "    0.010129387490451336,\n", "    0.024528631940484047,\n", "    0.08227021992206573,\n", "    0.03605559840798378,\n", "    -0.0657523050904274,\n", "    -0.01604563370347023,\n", "    -0.09240411967039108,\n", "    -0.03587576374411583,\n", "    -0.015764029696583748,\n", "    -0.01771974004805088,\n", "    0.0292818583548069,\n", "    -0.043902963399887085,\n", "    0.12264244258403778,\n", "    0.05427519232034683,\n", "    -0.020925089716911316,\n", "    0.008841264992952347,\n", "    0.0446908175945282,\n", "    0.03423025831580162,\n", "    -0.09589555114507675,\n", "    -0.017467224970459938,\n", "    0.02902543731033802,\n", "    -0.0076433042995631695,\n", "    0.06892433762550354,\n", "    -0.006534205749630928,\n", "    0.014778955839574337,\n", "    -0.03988812118768692,\n", "    -0.01942453905940056,\n", "    -0.028872337192296982,\n", "    0.01113588735461235,\n", "    0.013420273549854755,\n", "    -0.019998928532004356,\n", "    -0.00506659597158432,\n", "    -0.013182270340621471,\n", "    0.05584825575351715,\n", "    0.048810768872499466,\n", "    0.011304874904453754,\n", "    0.06552490592002869,\n", "    0.015346077270805836,\n", "    0.10614272207021713,\n", "    0.034579794853925705,\n", "    0.013331983238458633,\n", "    0.05422965809702873,\n", "    0.02604549191892147,\n", "    -7.268357812306647e-33,\n", "    -0.08047550916671753,\n", "    -0.034264229238033295,\n", "    -0.001368471421301365,\n", "    0.061961829662323,\n", "    0.00018546193314250559,\n", "    0.010903812013566494,\n", "    0.013502643443644047,\n", "    -0.10978782922029495,\n", "    -0.03210373595356941,\n", "    0.09519875049591064,\n", "    0.017290782183408737,\n", "    0.02678215689957142,\n", "    0.054302677512168884,\n", "    -0.010659797117114067,\n", "    0.015641823410987854,\n", "    0.015898898243904114,\n", "    0.0178837887942791,\n", "    0.061668865382671356,\n", "    -0.07701368629932404,\n", "    0.007275603711605072,\n", "    0.04147257283329964,\n", "    -0.0289072897285223,\n", "    -0.020715605467557907,\n", "    0.07605861127376556,\n", "    -0.07754907757043839,\n", "    -0.0051954262889921665,\n", "    0.033195171505212784,\n", "    -0.021545134484767914,\n", "    -0.046516429632902145,\n", "    0.023455677554011345,\n", "    0.0010292807128280401,\n", "    0.023822983726859093,\n", "    -0.00040830508805811405,\n", "    -0.016061443835496902,\n", "    -0.0014011417515575886,\n", "    -0.054777245968580246,\n", "    0.02128174714744091,\n", "    0.08693793416023254,\n", "    0.030528023838996887,\n", "    0.09517760574817657,\n", "    -0.040486253798007965,\n", "    0.0030016573145985603,\n", "    0.0714912936091423,\n", "    0.12273942679166794,\n", "    0.07334781438112259,\n", "    -0.008634588681161404,\n", "    -0.10305096209049225,\n", "    -0.006476451642811298,\n", "    0.05654062703251839,\n", "    -0.07300769537687302,\n", "    0.024300821125507355,\n", "    0.03566674143075943,\n", "    -0.04626433923840523,\n", "    -0.04139340668916702,\n", "    0.047267261892557144,\n", "    0.08077110350131989,\n", "    0.03470112755894661,\n", "    0.0058949049562215805,\n", "    -0.10028844326734543,\n", "    0.03749828785657883,\n", "    -0.0665869489312172,\n", "    -0.15024447441101074,\n", "    0.016482988372445107,\n", "    -0.020662877708673477],\n", "   'text_vector': [0.043848298490047455,\n", "    -0.1254618912935257,\n", "    -0.061072494834661484,\n", "    -0.053048256784677505,\n", "    0.02899436093866825,\n", "    0.07208551466464996,\n", "    -0.04655485227704048,\n", "    0.018333731219172478,\n", "    -0.06302349269390106,\n", "    0.0234158206731081,\n", "    -0.021521782502532005,\n", "    -0.0021427010651677847,\n", "    -0.042596712708473206,\n", "    -0.04608084261417389,\n", "    0.09751750528812408,\n", "    -0.03156450763344765,\n", "    0.06600627303123474,\n", "    -0.05940822884440422,\n", "    -0.08235721290111542,\n", "    0.004348590970039368,\n", "    0.02088913880288601,\n", "    -0.004981386009603739,\n", "    -0.030742783099412918,\n", "    -0.025681814178824425,\n", "    -0.01180611364543438,\n", "    -0.08406428247690201,\n", "    0.024000054225325584,\n", "    0.008813265711069107,\n", "    0.01507385540753603,\n", "    -0.005195570643991232,\n", "    -0.0349549762904644,\n", "    -0.06412602961063385,\n", "    0.03309622406959534,\n", "    0.027923613786697388,\n", "    0.17698751389980316,\n", "    0.034031305462121964,\n", "    0.034437574446201324,\n", "    -0.029336202889680862,\n", "    -0.030172424390912056,\n", "    -0.04690512269735336,\n", "    -0.0016040572663769126,\n", "    -0.10399436205625534,\n", "    -0.0696716383099556,\n", "    0.013558016158640385,\n", "    0.03466144576668739,\n", "    -0.012306774966418743,\n", "    -0.02126174606382847,\n", "    -0.1195206493139267,\n", "    0.018996937200427055,\n", "    -0.028095124289393425,\n", "    0.02349996007978916,\n", "    -0.052048325538635254,\n", "    0.060330312699079514,\n", "    0.10230772942304611,\n", "    -0.02620256133377552,\n", "    0.00523729482665658,\n", "    0.009556458331644535,\n", "    0.06566428393125534,\n", "    0.013845846988260746,\n", "    0.006886104587465525,\n", "    -0.021942416206002235,\n", "    -0.06505310535430908,\n", "    -0.0037587566766887903,\n", "    0.08025620877742767,\n", "    -0.024756496772170067,\n", "    0.04343198612332344,\n", "    -0.0011374464957043529,\n", "    0.05191487818956375,\n", "    0.07633676379919052,\n", "    -0.10254829376935959,\n", "    -0.026342283934354782,\n", "    -0.028088580816984177,\n", "    -0.01674129255115986,\n", "    0.06010809913277626,\n", "    0.09119699150323868,\n", "    -0.0375593826174736,\n", "    0.08737782388925552,\n", "    0.010631080716848373,\n", "    0.029285090044140816,\n", "    0.03736163303256035,\n", "    -0.02383832260966301,\n", "    -0.05091886594891548,\n", "    0.015257341787219048,\n", "    -0.019891707226634026,\n", "    -0.11693789064884186,\n", "    0.022571232169866562,\n", "    0.040669750422239304,\n", "    0.009113574400544167,\n", "    0.02979406528174877,\n", "    -0.005801327060908079,\n", "    -0.04585640877485275,\n", "    0.07229558378458023,\n", "    -0.030513491481542587,\n", "    -0.03806297481060028,\n", "    -0.100631482899189,\n", "    0.0313897430896759,\n", "    -0.022437050938606262,\n", "    -0.038303226232528687,\n", "    -0.06837113946676254,\n", "    0.008124355226755142,\n", "    -0.03529668226838112,\n", "    0.00648092245683074,\n", "    -0.00879316870123148,\n", "    0.04359956458210945,\n", "    0.04554462060332298,\n", "    0.015635402873158455,\n", "    0.033292051404714584,\n", "    0.012278405018150806,\n", "    0.038172196596860886,\n", "    0.007188647519797087,\n", "    -0.01659783162176609,\n", "    0.04898623749613762,\n", "    0.08313527703285217,\n", "    0.00651817861944437,\n", "    0.04880943149328232,\n", "    0.019413089379668236,\n", "    0.02528362162411213,\n", "    0.0007505073444917798,\n", "    0.004681425169110298,\n", "    -0.0858713760972023,\n", "    0.028141489252448082,\n", "    0.03868052363395691,\n", "    0.027844024822115898,\n", "    0.0012848410988226533,\n", "    0.009979343973100185,\n", "    -0.01998627372086048,\n", "    -0.019983777776360512,\n", "    2.1430037841761844e-31,\n", "    -0.01707853376865387,\n", "    -0.11647506803274155,\n", "    0.0012942771427333355,\n", "    -0.03343958035111427,\n", "    -0.01673273555934429,\n", "    -0.026380019262433052,\n", "    0.014754134230315685,\n", "    0.09485512226819992,\n", "    -0.010283012874424458,\n", "    -0.0530816912651062,\n", "    -0.019617609679698944,\n", "    -0.10690317302942276,\n", "    -0.027107205241918564,\n", "    -0.09844356030225754,\n", "    0.017822960391640663,\n", "    -0.11037316173315048,\n", "    -0.009109271690249443,\n", "    -0.04316789284348488,\n", "    -0.012452243827283382,\n", "    0.050013333559036255,\n", "    -0.05058759078383446,\n", "    -0.07681926339864731,\n", "    -0.021854398772120476,\n", "    0.005375050939619541,\n", "    0.06376472115516663,\n", "    -0.03692249953746796,\n", "    0.06875228136777878,\n", "    -0.0035264315083622932,\n", "    0.08631670475006104,\n", "    0.07395897805690765,\n", "    -0.05962805077433586,\n", "    -0.014695707708597183,\n", "    -0.025829127058386803,\n", "    0.020557083189487457,\n", "    -0.02396482601761818,\n", "    0.007506120018661022,\n", "    0.04224453493952751,\n", "    0.05357489734888077,\n", "    -0.015257112681865692,\n", "    -0.03975173830986023,\n", "    0.04872946813702583,\n", "    0.006662525702267885,\n", "    -0.039725955575704575,\n", "    -0.0003279184747952968,\n", "    -0.050898194313049316,\n", "    -0.08246544748544693,\n", "    0.04465582221746445,\n", "    -0.004997145850211382,\n", "    0.09899875521659851,\n", "    -0.013527567498385906,\n", "    0.04374847561120987,\n", "    -0.06362444907426834,\n", "    -0.06520465761423111,\n", "    0.04955549165606499,\n", "    0.10975948721170425,\n", "    0.03118995949625969,\n", "    -0.01725468598306179,\n", "    0.01816556602716446,\n", "    0.025086604058742523,\n", "    0.014693457633256912,\n", "    -0.03420139476656914,\n", "    -0.0487113818526268,\n", "    0.03192918747663498,\n", "    -0.06503073126077652,\n", "    0.05729658901691437,\n", "    0.07118617743253708,\n", "    0.058363188058137894,\n", "    -0.022414522245526314,\n", "    0.026150330901145935,\n", "    -0.03915569931268692,\n", "    -0.1205689013004303,\n", "    -0.057819049805402756,\n", "    -0.026732340455055237,\n", "    -0.034070562571287155,\n", "    -0.012529654428362846,\n", "    0.05374981090426445,\n", "    -0.05202769488096237,\n", "    0.02884458377957344,\n", "    0.06405103206634521,\n", "    0.003282726975157857,\n", "    0.16692939400672913,\n", "    0.0041356743313372135,\n", "    0.050722140818834305,\n", "    0.07105757296085358,\n", "    -0.03119891695678234,\n", "    -0.029061509296298027,\n", "    0.07467754185199738,\n", "    -0.14077050983905792,\n", "    0.06461381167173386,\n", "    0.01626870408654213,\n", "    -0.14964936673641205,\n", "    0.00012030945072183385,\n", "    -0.05369679629802704,\n", "    0.10469949245452881,\n", "    -0.0002383906248724088,\n", "    -4.774256346869305e-33,\n", "    -0.0444001741707325,\n", "    -0.049976952373981476,\n", "    -0.0998464971780777,\n", "    0.06014134734869003,\n", "    -0.01827739179134369,\n", "    0.008540657348930836,\n", "    -0.03284076973795891,\n", "    -0.01657511666417122,\n", "    0.023106174543499947,\n", "    0.040776632726192474,\n", "    -0.0013237199746072292,\n", "    -0.02881219983100891,\n", "    -0.0977262631058693,\n", "    0.1011791080236435,\n", "    -0.009082787670195103,\n", "    0.0168710108846426,\n", "    -0.008619760163128376,\n", "    0.0141745675355196,\n", "    -0.08511300384998322,\n", "    -0.0039186496287584305,\n", "    -0.11443479359149933,\n", "    -0.08996365964412689,\n", "    -0.0028725815936923027,\n", "    -0.05505723878741264,\n", "    0.03220602869987488,\n", "    0.05169014632701874,\n", "    0.03767123445868492,\n", "    0.10779951512813568,\n", "    -0.023266548290848732,\n", "    -0.028311865404248238,\n", "    -0.0012997330632060766,\n", "    -0.019226958975195885,\n", "    -0.021215809509158134,\n", "    -0.022601915523409843,\n", "    -6.585268420167267e-05,\n", "    0.06340474635362625,\n", "    0.019764283671975136,\n", "    -0.05079228803515434,\n", "    -0.03610016778111458,\n", "    -0.005555153824388981,\n", "    0.08380355685949326,\n", "    -0.022395044565200806,\n", "    -0.05751200020313263,\n", "    -0.024056199938058853,\n", "    -0.05344335734844208,\n", "    0.009779159910976887,\n", "    -0.012034106999635696,\n", "    -0.0024058667477220297,\n", "    -0.092363640666008,\n", "    0.01917940564453602,\n", "    -0.001415858743712306,\n", "    -0.009669620543718338,\n", "    -0.013162032701075077,\n", "    0.03106294944882393,\n", "    -0.00907102320343256,\n", "    0.1309370994567871,\n", "    0.00429749209433794,\n", "    0.09932132810354233,\n", "    0.07377496361732483,\n", "    -0.11003679037094116,\n", "    0.04840485379099846,\n", "    -0.020946554839611053,\n", "    -0.051813576370477676,\n", "    -0.058171894401311874,\n", "    0.11238298565149307,\n", "    -0.027328303083777428,\n", "    0.11887703835964203,\n", "    -0.0020374141167849302,\n", "    -0.09125135838985443,\n", "    0.057854048907756805,\n", "    0.024741560220718384,\n", "    -0.017046315595507622,\n", "    0.02250911481678486,\n", "    0.02335326559841633,\n", "    0.0022734454832971096,\n", "    0.01729760505259037,\n", "    0.011923490092158318,\n", "    -0.07979344576597214,\n", "    -0.04576591029763222,\n", "    0.011349405162036419,\n", "    -0.0070466841571033,\n", "    0.09553288668394089,\n", "    0.0003317225491628051,\n", "    0.018468743190169334,\n", "    0.013607163913547993,\n", "    0.06512632966041565,\n", "    0.02133674919605255,\n", "    0.033928170800209045,\n", "    0.024519752711057663,\n", "    -0.03189784660935402,\n", "    -0.04380952566862106,\n", "    -0.03001801297068596,\n", "    -0.030510906130075455,\n", "    0.04725204035639763,\n", "    0.05420922115445137,\n", "    -4.459979115969335e-33,\n", "    0.03770555555820465,\n", "    0.02877175249159336,\n", "    -0.03881480172276497,\n", "    0.04849077761173248,\n", "    0.03498085215687752,\n", "    -0.009872992523014545,\n", "    -0.014510607346892357,\n", "    -0.02184194140136242,\n", "    -0.008951811119914055,\n", "    0.04898983612656593,\n", "    -0.005851638503372669,\n", "    0.025399083271622658,\n", "    -0.05902611091732979,\n", "    0.0593842975795269,\n", "    -0.015387960709631443,\n", "    -0.007984361611306667,\n", "    0.03465378284454346,\n", "    -0.023967772722244263,\n", "    -0.026189051568508148,\n", "    -0.04737922176718712,\n", "    -0.0310523584485054,\n", "    0.0381569042801857,\n", "    -0.0005338017945177853,\n", "    0.05461324378848076,\n", "    -0.00929213222116232,\n", "    0.00884532742202282,\n", "    0.04521811380982399,\n", "    0.0668286606669426,\n", "    0.031729649752378464,\n", "    -0.02303357422351837,\n", "    -0.03784777596592903,\n", "    0.018672991544008255,\n", "    0.062268029898405075,\n", "    -0.01145622693002224,\n", "    0.048490773886442184,\n", "    0.050635240972042084,\n", "    -0.0704546645283699,\n", "    0.004085977096110582,\n", "    -0.02190437912940979,\n", "    0.07842233777046204,\n", "    0.02367493323981762,\n", "    0.01140880398452282,\n", "    0.07945019751787186,\n", "    0.060053084045648575,\n", "    -0.004338860511779785,\n", "    0.05272359773516655,\n", "    0.025944121181964874,\n", "    -0.014712352305650711,\n", "    0.08057834953069687,\n", "    -0.006758374162018299,\n", "    -0.0357932411134243,\n", "    -0.026162805035710335,\n", "    -0.06348241865634918,\n", "    -0.014419949613511562,\n", "    -0.06071990355849266,\n", "    -0.024742422625422478,\n", "    -0.00846546608954668,\n", "    -0.021864669397473335,\n", "    -0.07817551493644714,\n", "    0.0018378508975729346,\n", "    -0.06802698224782944,\n", "    0.012350248172879219,\n", "    -0.04516179859638214,\n", "    -0.03823855146765709],\n", "   'question_text_vector': [0.034988049417734146,\n", "    -0.1272173374891281,\n", "    -0.06336934864521027,\n", "    -0.050240062177181244,\n", "    0.02855716273188591,\n", "    0.09449565410614014,\n", "    -0.05769529938697815,\n", "    0.015875397250056267,\n", "    -0.11558769643306732,\n", "    -0.011012040078639984,\n", "    -0.03365906700491905,\n", "    0.004123943857848644,\n", "    -0.034827765077352524,\n", "    -0.03910587355494499,\n", "    0.0973118394613266,\n", "    -0.04279550909996033,\n", "    0.1093439906835556,\n", "    -0.03087799623608589,\n", "    -0.05652404949069023,\n", "    0.02215481735765934,\n", "    -0.04013540968298912,\n", "    -0.045817043632268906,\n", "    -0.01687776856124401,\n", "    -0.0035670448560267687,\n", "    -0.008294506929814816,\n", "    -0.08317629247903824,\n", "    0.009879411198198795,\n", "    -0.007825049571692944,\n", "    0.014485973864793777,\n", "    -0.017029203474521637,\n", "    -0.004591908305883408,\n", "    -0.052269212901592255,\n", "    0.040421172976493835,\n", "    0.02458982542157173,\n", "    0.11970154941082001,\n", "    0.0298873633146286,\n", "    0.021559137850999832,\n", "    -0.029543913900852203,\n", "    -0.07946471869945526,\n", "    -0.032349590212106705,\n", "    -0.01393610518425703,\n", "    -0.06485845893621445,\n", "    -0.02789134904742241,\n", "    0.02209382876753807,\n", "    0.0808495506644249,\n", "    -0.0048005785793066025,\n", "    -0.03642566502094269,\n", "    -0.11458669602870941,\n", "    0.02856632135808468,\n", "    -0.019043684005737305,\n", "    -0.012130939401686192,\n", "    -0.05334636941552162,\n", "    0.05147731304168701,\n", "    0.08566968142986298,\n", "    -0.0396624393761158,\n", "    0.02891254425048828,\n", "    -0.012891883961856365,\n", "    0.09447748959064484,\n", "    -0.012683887965977192,\n", "    0.022503698244690895,\n", "    0.001367673627100885,\n", "    -0.07215745002031326,\n", "    -0.021239256486296654,\n", "    0.05850530043244362,\n", "    -0.008301991969347,\n", "    0.058567896485328674,\n", "    0.007492804434150457,\n", "    0.08206475526094437,\n", "    0.11372020840644836,\n", "    -0.07604628801345825,\n", "    -0.05465082451701164,\n", "    -0.028535328805446625,\n", "    0.010401259176433086,\n", "    0.061145633459091187,\n", "    0.10989761352539062,\n", "    -0.014652282930910587,\n", "    0.06458261609077454,\n", "    -0.04355577751994133,\n", "    0.07980798184871674,\n", "    0.025252528488636017,\n", "    -0.023524748161435127,\n", "    -0.03748299181461334,\n", "    0.01125356089323759,\n", "    0.007210104260593653,\n", "    -0.07791809737682343,\n", "    -0.014989970251917839,\n", "    0.05427996441721916,\n", "    0.043044041842222214,\n", "    0.023687396198511124,\n", "    0.0061760335229337215,\n", "    -0.01805151253938675,\n", "    0.06394658982753754,\n", "    -0.037202779203653336,\n", "    -0.03895822912454605,\n", "    -0.10521047562360764,\n", "    0.04998749867081642,\n", "    -0.024069812148809433,\n", "    -0.08595363050699234,\n", "    -0.09851668030023575,\n", "    0.034920673817396164,\n", "    -0.06306038051843643,\n", "    -0.010259228758513927,\n", "    -0.024456897750496864,\n", "    0.04330960288643837,\n", "    -0.0004160708631388843,\n", "    -0.013106626458466053,\n", "    0.06361540406942368,\n", "    0.04486525431275368,\n", "    0.05044123902916908,\n", "    0.009200308471918106,\n", "    -0.011091835796833038,\n", "    0.08234376460313797,\n", "    0.058420468121767044,\n", "    0.00777260260656476,\n", "    0.015323664993047714,\n", "    0.006904187612235546,\n", "    0.024191085249185562,\n", "    0.014831319451332092,\n", "    0.03655656427145004,\n", "    -0.05594916641712189,\n", "    0.0488901324570179,\n", "    0.028312265872955322,\n", "    0.03328520432114601,\n", "    -0.03959156945347786,\n", "    0.012964262627065182,\n", "    -0.05178283900022507,\n", "    -0.036672212183475494,\n", "    1.4295953222666575e-31,\n", "    -0.009969166480004787,\n", "    -0.12370230257511139,\n", "    0.012136625126004219,\n", "    -0.01394582912325859,\n", "    0.015181873925030231,\n", "    -0.05020994693040848,\n", "    0.01737144961953163,\n", "    0.10232391953468323,\n", "    -0.010830973275005817,\n", "    -0.055464886128902435,\n", "    -0.0067628370597958565,\n", "    -0.07459104806184769,\n", "    0.006825887132436037,\n", "    -0.08529141545295715,\n", "    0.03447667881846428,\n", "    -0.12851747870445251,\n", "    -0.013580800034105778,\n", "    -0.029553625732660294,\n", "    -0.008247710764408112,\n", "    0.0323723703622818,\n", "    -0.018392307683825493,\n", "    -0.10226624459028244,\n", "    -0.022259686142206192,\n", "    -0.05178666114807129,\n", "    0.08484253287315369,\n", "    -0.0467267669737339,\n", "    0.06982139497995377,\n", "    0.00784284807741642,\n", "    0.09609442204236984,\n", "    0.08524288237094879,\n", "    -0.039456456899642944,\n", "    -0.007606768049299717,\n", "    -0.08724833279848099,\n", "    -0.01295989565551281,\n", "    -0.015215096063911915,\n", "    0.011139258742332458,\n", "    0.017784271389245987,\n", "    0.040409959852695465,\n", "    0.004095445387065411,\n", "    -0.06598956882953644,\n", "    -0.00832431111484766,\n", "    0.005845021456480026,\n", "    -0.011561548337340355,\n", "    -0.025998590514063835,\n", "    -0.06632231175899506,\n", "    -0.05538230016827583,\n", "    0.08299902081489563,\n", "    -0.019063271582126617,\n", "    0.05573422089219093,\n", "    -0.003312009619548917,\n", "    4.1517931094858795e-05,\n", "    -0.05449541285634041,\n", "    -0.07742122560739517,\n", "    0.02991289459168911,\n", "    0.12846063077449799,\n", "    -0.00958638172596693,\n", "    0.00295726559124887,\n", "    0.06173301115632057,\n", "    0.026929084211587906,\n", "    0.0345982201397419,\n", "    -0.03335653990507126,\n", "    -0.06830953806638718,\n", "    0.02496342547237873,\n", "    -0.03950900956988335,\n", "    0.022533904761075974,\n", "    0.048149410635232925,\n", "    0.004675874020904303,\n", "    -0.0823911726474762,\n", "    0.044452108442783356,\n", "    -0.034419912844896317,\n", "    -0.10572643578052521,\n", "    -0.05433044210076332,\n", "    -0.005099529400467873,\n", "    -0.04003743454813957,\n", "    -0.012911147437989712,\n", "    0.04810281842947006,\n", "    -0.06994742155075073,\n", "    -0.0021362649276852608,\n", "    0.026695767417550087,\n", "    -0.012767007574439049,\n", "    0.10703051835298538,\n", "    -0.0033233636058866978,\n", "    0.03202839940786362,\n", "    0.06497602164745331,\n", "    0.009112201631069183,\n", "    -0.013894637115299702,\n", "    0.039979420602321625,\n", "    -0.10783211141824722,\n", "    0.09475815296173096,\n", "    0.02556448243558407,\n", "    -0.1527215540409088,\n", "    -0.01914219558238983,\n", "    -0.04484780505299568,\n", "    0.0690835490822792,\n", "    -0.02003142610192299,\n", "    -5.210641726880786e-33,\n", "    -0.043311066925525665,\n", "    -0.040826279670000076,\n", "    -0.07491394877433777,\n", "    0.04532015323638916,\n", "    0.015480644069612026,\n", "    0.012089421041309834,\n", "    -0.037902552634477615,\n", "    -0.027079595252871513,\n", "    0.05351373553276062,\n", "    0.04659045487642288,\n", "    -0.017052961513400078,\n", "    -0.03759022802114487,\n", "    -0.07600848376750946,\n", "    0.07350818812847137,\n", "    -0.018431419506669044,\n", "    -0.007700092624872923,\n", "    -0.04614775627851486,\n", "    0.006330094300210476,\n", "    -0.043914396315813065,\n", "    -0.012705952860414982,\n", "    -0.0939432829618454,\n", "    -0.0212956964969635,\n", "    0.01157983485609293,\n", "    -0.033709000796079636,\n", "    0.0536433570086956,\n", "    0.05707726627588272,\n", "    0.020037174224853516,\n", "    0.12758389115333557,\n", "    -0.0032055152114480734,\n", "    -0.03458530455827713,\n", "    0.032934024930000305,\n", "    -0.05065232515335083,\n", "    -0.0568389967083931,\n", "    -0.07932087779045105,\n", "    0.01682988926768303,\n", "    0.09592048823833466,\n", "    -0.012683122418820858,\n", "    -0.025441637262701988,\n", "    -0.07672829180955887,\n", "    0.00900755263864994,\n", "    0.10099372267723083,\n", "    -0.047012533992528915,\n", "    -0.05738136172294617,\n", "    -0.06366457045078278,\n", "    -0.015801940113306046,\n", "    -0.0027351013850420713,\n", "    0.017153874039649963,\n", "    -0.02085891366004944,\n", "    -0.09437897801399231,\n", "    0.03475014865398407,\n", "    -0.021986857056617737,\n", "    -0.029415154829621315,\n", "    0.010047155432403088,\n", "    0.028930988162755966,\n", "    0.014842006377875805,\n", "    0.10413667559623718,\n", "    -0.02592306397855282,\n", "    0.07654622942209244,\n", "    0.030844856053590775,\n", "    -0.08183681219816208,\n", "    0.032801032066345215,\n", "    -0.03573104366660118,\n", "    -0.01876663789153099,\n", "    -0.04430897906422615,\n", "    0.12910446524620056,\n", "    -0.0024633232969790697,\n", "    0.10325764864683151,\n", "    0.0101528475061059,\n", "    -0.05478981137275696,\n", "    0.0738551989197731,\n", "    -0.007851135917007923,\n", "    -0.01575905829668045,\n", "    0.049912311136722565,\n", "    0.01382237859070301,\n", "    0.011220062151551247,\n", "    0.007271017879247665,\n", "    0.02178943157196045,\n", "    -0.07338076829910278,\n", "    -0.04998769238591194,\n", "    0.0027013500221073627,\n", "    0.00313735892996192,\n", "    0.09446769952774048,\n", "    0.00415147515013814,\n", "    0.04058067500591278,\n", "    -0.013521664775907993,\n", "    0.0853525847196579,\n", "    0.050586998462677,\n", "    0.03571903333067894,\n", "    0.03997667878866196,\n", "    -0.019772149622440338,\n", "    0.004319861531257629,\n", "    -0.01483541913330555,\n", "    -0.03107890486717224,\n", "    0.06846976280212402,\n", "    0.08587584644556046,\n", "    -5.818533569387162e-33,\n", "    0.014702065847814083,\n", "    0.010581939481198788,\n", "    -0.057275161147117615,\n", "    0.05980880558490753,\n", "    0.011840962804853916,\n", "    0.004935455974191427,\n", "    -0.0254835058003664,\n", "    -0.0483231283724308,\n", "    -0.02000593952834606,\n", "    0.0642227828502655,\n", "    0.012042857706546783,\n", "    0.0003551678382791579,\n", "    -0.019545473158359528,\n", "    0.06100963056087494,\n", "    0.00718469126150012,\n", "    -0.003008622443303466,\n", "    0.04847976192831993,\n", "    0.043041300028562546,\n", "    -0.06993840634822845,\n", "    -0.04959075525403023,\n", "    0.0035297067370265722,\n", "    0.02935168147087097,\n", "    0.00664037000387907,\n", "    0.050002556294202805,\n", "    -0.031778521835803986,\n", "    0.040973588824272156,\n", "    0.06466133147478104,\n", "    0.06947030872106552,\n", "    0.005644429009407759,\n", "    -0.018311776220798492,\n", "    -0.03790275752544403,\n", "    0.026603681966662407,\n", "    0.04266213998198509,\n", "    -0.00982225127518177,\n", "    0.036880094558000565,\n", "    0.018767114728689194,\n", "    -0.04780692607164383,\n", "    0.03502565994858742,\n", "    -0.010775269009172916,\n", "    0.09922222793102264,\n", "    -0.010082586668431759,\n", "    0.03297402337193489,\n", "    0.07986769825220108,\n", "    0.0851263627409935,\n", "    0.05647525191307068,\n", "    0.035993605852127075,\n", "    -0.028410349041223526,\n", "    -0.020861981436610222,\n", "    0.07864023745059967,\n", "    -0.01599428616464138,\n", "    -0.006704321131110191,\n", "    -0.015610952861607075,\n", "    -0.06029416620731354,\n", "    -0.012689273804426193,\n", "    -0.02352318726480007,\n", "    0.023268986493349075,\n", "    0.008128177374601364,\n", "    -0.016637129709124565,\n", "    -0.09191059321165085,\n", "    0.015235038474202156,\n", "    -0.08356419205665588,\n", "    -0.043649062514305115,\n", "    -0.03742843121290207,\n", "    -0.01631040684878826]}},\n", " {'_index': 'course-questions',\n", "  '_id': 'ISPvR5EBO7bpWV6zkHWd',\n", "  '_score': 18.261362,\n", "  '_source': {'text': \"You don't need it. You're accepted. You can also just start learning and submitting homework without registering. It is not checked against any registered list. Registration is just to gauge interest before the start date.\",\n", "   'section': 'General course-related questions',\n", "   'question': 'Course - I have registered for the Data Engineering Bootcamp. When can I expect to receive the confirmation email?',\n", "   'course': 'data-engineering-zoomcamp',\n", "   'id': '0bbf41ec',\n", "   'question_vector': [-0.05679399147629738,\n", "    -0.020690998062491417,\n", "    0.07392720878124237,\n", "    0.0077827610075473785,\n", "    0.09063708037137985,\n", "    -0.013811575248837471,\n", "    0.015122291631996632,\n", "    -0.02677157148718834,\n", "    0.03761368989944458,\n", "    0.000635450123809278,\n", "    -0.030522145330905914,\n", "    -0.023857278749346733,\n", "    0.0023214418906718493,\n", "    -0.00884835235774517,\n", "    -0.04615911468863487,\n", "    0.021097300574183464,\n", "    0.03577867150306702,\n", "    -0.16195042431354523,\n", "    0.027937738224864006,\n", "    -0.03133610263466835,\n", "    -0.02814851701259613,\n", "    -0.06264662742614746,\n", "    0.0010353572433814406,\n", "    -0.014973482117056847,\n", "    0.0009825065499171615,\n", "    -0.001572989160194993,\n", "    0.09091535210609436,\n", "    0.0032263989560306072,\n", "    -0.037844691425561905,\n", "    0.0023443244863301516,\n", "    -0.054174866527318954,\n", "    -0.03219340369105339,\n", "    0.11002841591835022,\n", "    0.04554295167326927,\n", "    0.07462435215711594,\n", "    0.00753997266292572,\n", "    0.05388643965125084,\n", "    -0.04560663551092148,\n", "    0.017347000539302826,\n", "    -0.01885615661740303,\n", "    -0.09881918877363205,\n", "    -0.08505131304264069,\n", "    -0.005246605258435011,\n", "    0.056509844958782196,\n", "    0.05821625143289566,\n", "    0.016933312639594078,\n", "    0.00879947654902935,\n", "    -0.004137267824262381,\n", "    -0.08232574909925461,\n", "    0.019278263673186302,\n", "    0.01670082099735737,\n", "    -0.05737268924713135,\n", "    0.04201402887701988,\n", "    -0.016148274764418602,\n", "    -0.044490449130535126,\n", "    0.07850099354982376,\n", "    -0.006565871182829142,\n", "    -0.033523280173540115,\n", "    -0.0445580817759037,\n", "    0.0886392667889595,\n", "    -0.04970385134220123,\n", "    -0.06909290701150894,\n", "    -0.0703585296869278,\n", "    0.0003870188375003636,\n", "    -0.021096518263220787,\n", "    -0.034157805144786835,\n", "    -0.06136461719870567,\n", "    -0.031891342252492905,\n", "    0.06027901917695999,\n", "    -0.146852046251297,\n", "    0.017397629097104073,\n", "    -0.04324018582701683,\n", "    0.03178780525922775,\n", "    0.06645966321229935,\n", "    0.04201045632362366,\n", "    0.03966682031750679,\n", "    0.031694646924734116,\n", "    0.07783297449350357,\n", "    0.06639218330383301,\n", "    0.023238029330968857,\n", "    -0.10691534727811813,\n", "    0.07023731619119644,\n", "    0.0031173047609627247,\n", "    -0.02528569847345352,\n", "    0.05426114425063133,\n", "    0.06288029253482819,\n", "    0.08303713798522949,\n", "    0.16919764876365662,\n", "    -0.021433014422655106,\n", "    0.047813862562179565,\n", "    0.07729988545179367,\n", "    -0.003517682431265712,\n", "    -0.08059106767177582,\n", "    0.02496335655450821,\n", "    -0.0637025535106659,\n", "    0.11070314049720764,\n", "    -0.060942839831113815,\n", "    -0.05870356783270836,\n", "    0.014508859254419804,\n", "    -0.05709947645664215,\n", "    0.014955285005271435,\n", "    0.06830032914876938,\n", "    -0.018237384036183357,\n", "    -0.08037698268890381,\n", "    -0.0702236071228981,\n", "    -0.022935962304472923,\n", "    -0.012347385287284851,\n", "    -0.03649849444627762,\n", "    0.001014095963910222,\n", "    0.0020454025361686945,\n", "    0.02133597433567047,\n", "    -0.003615456633269787,\n", "    0.06696908175945282,\n", "    0.05910065770149231,\n", "    -0.061029378324747086,\n", "    0.07868289202451706,\n", "    -0.07810476422309875,\n", "    0.1258605569601059,\n", "    0.04574967920780182,\n", "    -0.07126152515411377,\n", "    -0.060091532766819,\n", "    -0.008815092034637928,\n", "    0.07058785855770111,\n", "    -0.08840249478816986,\n", "    -0.0015889555215835571,\n", "    -0.12038875371217728,\n", "    -0.00931333377957344,\n", "    7.339118575545356e-31,\n", "    -0.02295377105474472,\n", "    -0.047967515885829926,\n", "    -0.04883074760437012,\n", "    -0.05875964090228081,\n", "    -0.014754225499927998,\n", "    -0.10791928321123123,\n", "    0.07895864546298981,\n", "    -0.03299699351191521,\n", "    0.011250113137066364,\n", "    0.006257232744246721,\n", "    -0.08170060068368912,\n", "    0.023174526169896126,\n", "    0.07356888800859451,\n", "    -0.08027368038892746,\n", "    -0.0685398280620575,\n", "    0.03387565538287163,\n", "    -0.11478430032730103,\n", "    0.038145292550325394,\n", "    -0.05185288190841675,\n", "    0.014533916488289833,\n", "    0.04027204215526581,\n", "    -0.038932543247938156,\n", "    0.013744572177529335,\n", "    -0.008044038899242878,\n", "    0.0890813022851944,\n", "    -0.027120277285575867,\n", "    -0.030318010598421097,\n", "    0.11113081127405167,\n", "    0.004645610228180885,\n", "    0.040048010647296906,\n", "    -0.016688520088791847,\n", "    -0.022485166788101196,\n", "    0.04944441840052605,\n", "    0.015815813094377518,\n", "    0.018420513719320297,\n", "    0.03379583731293678,\n", "    0.024995850399136543,\n", "    0.02052309177815914,\n", "    0.016269775107502937,\n", "    0.015685493126511574,\n", "    0.06457466632127762,\n", "    -0.003136322135105729,\n", "    -0.031936705112457275,\n", "    -0.0374128632247448,\n", "    0.013246383517980576,\n", "    -0.03335944190621376,\n", "    0.057900816202163696,\n", "    -0.06710517406463623,\n", "    0.15754389762878418,\n", "    -0.06407306343317032,\n", "    -0.021190280094742775,\n", "    -0.0006949415546841919,\n", "    0.04869113117456436,\n", "    -0.012323208153247833,\n", "    -0.004445832688361406,\n", "    -0.0028223551344126463,\n", "    0.08504668623209,\n", "    0.04598943144083023,\n", "    0.006389595568180084,\n", "    0.062026262283325195,\n", "    -0.026437964290380478,\n", "    -0.026612820103764534,\n", "    0.10341198742389679,\n", "    0.010906419716775417,\n", "    -0.01998177543282509,\n", "    -0.06371940672397614,\n", "    -0.025325482711195946,\n", "    0.016375545412302017,\n", "    -0.013461112044751644,\n", "    0.053922299295663834,\n", "    0.04912330210208893,\n", "    -0.020158061757683754,\n", "    -0.017121391370892525,\n", "    -0.027088595554232597,\n", "    0.06002682447433472,\n", "    -0.02601713500916958,\n", "    -0.018770849332213402,\n", "    0.018097426742315292,\n", "    -0.017607754096388817,\n", "    0.013296011835336685,\n", "    0.018234040588140488,\n", "    -0.005450026132166386,\n", "    -0.07013405859470367,\n", "    0.004137820564210415,\n", "    0.06674226373434067,\n", "    0.057925064116716385,\n", "    0.05735378712415695,\n", "    0.055079180747270584,\n", "    -0.04690468683838844,\n", "    0.022718962281942368,\n", "    -0.10925596952438354,\n", "    -0.02696814015507698,\n", "    -0.03677070885896683,\n", "    0.025124626234173775,\n", "    0.04663657769560814,\n", "    -2.696196678663533e-33,\n", "    0.04886646941304207,\n", "    -0.011950808577239513,\n", "    0.014198753982782364,\n", "    -0.0432923398911953,\n", "    0.0011677646543830633,\n", "    -0.026071252301335335,\n", "    0.0694301426410675,\n", "    0.07305139303207397,\n", "    -0.04345877096056938,\n", "    0.016833825036883354,\n", "    0.08231005817651749,\n", "    -0.015291953459382057,\n", "    -0.05829322710633278,\n", "    0.009357016533613205,\n", "    -0.04816306009888649,\n", "    0.0033661341294646263,\n", "    0.04460746794939041,\n", "    -0.03927013278007507,\n", "    0.02675117366015911,\n", "    0.02475159242749214,\n", "    0.01738111488521099,\n", "    0.009382622316479683,\n", "    -0.07505808770656586,\n", "    -0.03206720948219299,\n", "    -0.01125972718000412,\n", "    -0.04902275279164314,\n", "    0.047359026968479156,\n", "    0.07736486196517944,\n", "    -0.02381090633571148,\n", "    0.007732495665550232,\n", "    0.02038664184510708,\n", "    -0.0057235960848629475,\n", "    -0.00817796029150486,\n", "    0.1024472638964653,\n", "    0.05647049471735954,\n", "    -0.058563243597745895,\n", "    0.04430575296282768,\n", "    -0.0561666339635849,\n", "    0.05956420302391052,\n", "    0.0674363449215889,\n", "    0.0974326804280281,\n", "    0.017200171947479248,\n", "    -0.04718208685517311,\n", "    0.007850436493754387,\n", "    -0.07171177864074707,\n", "    -0.03889293596148491,\n", "    0.01178681943565607,\n", "    0.01875188574194908,\n", "    0.005230132024735212,\n", "    0.002274629892781377,\n", "    0.004279536660760641,\n", "    0.03389240801334381,\n", "    0.07986907660961151,\n", "    0.012821595184504986,\n", "    -0.036124225705862045,\n", "    0.04822172224521637,\n", "    -0.02524404041469097,\n", "    0.0003214591124560684,\n", "    0.002068684669211507,\n", "    0.049870483577251434,\n", "    -0.0145103195682168,\n", "    0.03634299337863922,\n", "    0.14202718436717987,\n", "    -0.08096303045749664,\n", "    0.005163991823792458,\n", "    -0.02579345926642418,\n", "    0.030182402580976486,\n", "    0.03498333320021629,\n", "    -0.00013560584920924157,\n", "    0.06400922685861588,\n", "    0.02155374363064766,\n", "    -0.007027436047792435,\n", "    -0.09130162745714188,\n", "    -0.012107430025935173,\n", "    0.03545178100466728,\n", "    -0.00961567647755146,\n", "    0.027740905061364174,\n", "    -0.05384184047579765,\n", "    -0.06397280097007751,\n", "    -0.020510945469141006,\n", "    0.014339018613100052,\n", "    0.011573141440749168,\n", "    -0.053482212126255035,\n", "    0.06092703714966774,\n", "    0.04309920221567154,\n", "    0.04735909402370453,\n", "    -0.02122899889945984,\n", "    0.011230315081775188,\n", "    0.1254764050245285,\n", "    -0.04656323045492172,\n", "    0.06793372333049774,\n", "    -0.06677238643169403,\n", "    -0.02170800231397152,\n", "    0.01533699780702591,\n", "    -0.07273507863283157,\n", "    -3.773213071890722e-33,\n", "    -0.0013990952866151929,\n", "    -0.021468855440616608,\n", "    0.07794611901044846,\n", "    0.060317814350128174,\n", "    0.0021312162280082703,\n", "    0.044643595814704895,\n", "    -0.056475698947906494,\n", "    -0.05380137637257576,\n", "    -0.019825812429189682,\n", "    -0.06363086402416229,\n", "    0.05086604505777359,\n", "    -0.09028376638889313,\n", "    -0.027698349207639694,\n", "    -0.05281037837266922,\n", "    0.09752241522073746,\n", "    0.017686739563941956,\n", "    -0.04080435633659363,\n", "    -0.050916556268930435,\n", "    0.026918193325400352,\n", "    -0.08084427565336227,\n", "    -0.02231520228087902,\n", "    -0.0004596752696670592,\n", "    0.011242887936532497,\n", "    -0.02337300032377243,\n", "    0.05694485828280449,\n", "    0.08579117059707642,\n", "    0.0038234165403991938,\n", "    0.03918925300240517,\n", "    -0.12187916785478592,\n", "    -0.04324785992503166,\n", "    -0.028662480413913727,\n", "    -0.02600335143506527,\n", "    -0.00048420115490444005,\n", "    -0.036713436245918274,\n", "    -0.03158104419708252,\n", "    0.032932717353105545,\n", "    0.00780264800414443,\n", "    -0.004877663217484951,\n", "    0.018723076209425926,\n", "    -0.021205589175224304,\n", "    0.00869018118828535,\n", "    0.0523192435503006,\n", "    0.014819059520959854,\n", "    -0.004329094663262367,\n", "    -0.03575119376182556,\n", "    0.02746719680726528,\n", "    -0.1071644052863121,\n", "    -0.04415016248822212,\n", "    0.018879923969507217,\n", "    -0.04000646993517876,\n", "    -0.0038921386003494263,\n", "    0.014652871526777744,\n", "    -0.03331660106778145,\n", "    -0.024984486401081085,\n", "    0.02639438584446907,\n", "    0.04807571321725845,\n", "    -0.02543037384748459,\n", "    -0.0264485664665699,\n", "    0.020998645573854446,\n", "    0.009548514150083065,\n", "    -0.03832205757498741,\n", "    0.0036883435677736998,\n", "    -0.04909897595643997,\n", "    -0.0688103586435318],\n", "   'text_vector': [-0.04974236711859703,\n", "    -0.04570856690406799,\n", "    -0.07504702359437943,\n", "    0.007923182100057602,\n", "    0.04542521387338638,\n", "    0.05172204598784447,\n", "    -0.012163113802671432,\n", "    -0.017335286363959312,\n", "    -0.04204864799976349,\n", "    0.0011291841510683298,\n", "    -0.009097331203520298,\n", "    -0.0020967491436749697,\n", "    -0.007515956647694111,\n", "    0.0006566998781636357,\n", "    0.011032454669475555,\n", "    -0.000814230355899781,\n", "    0.062242213636636734,\n", "    -0.09616007655858994,\n", "    0.0774911418557167,\n", "    -0.09486758708953857,\n", "    -0.10643096268177032,\n", "    -0.032350458204746246,\n", "    -0.06946828961372375,\n", "    -0.0216531865298748,\n", "    0.05395311489701271,\n", "    -0.017155587673187256,\n", "    0.027293579652905464,\n", "    -0.033496223390102386,\n", "    0.023594480007886887,\n", "    0.04510877653956413,\n", "    0.020510371774435043,\n", "    -0.01455190684646368,\n", "    0.08836651593446732,\n", "    0.02632162906229496,\n", "    0.062034331262111664,\n", "    0.009635952301323414,\n", "    0.021510062739253044,\n", "    -0.008908987045288086,\n", "    -0.0074824001640081406,\n", "    -0.07599471509456635,\n", "    -0.011622200720012188,\n", "    -0.0801810771226883,\n", "    -0.018636949360370636,\n", "    0.06837303936481476,\n", "    -0.0011060317046940327,\n", "    0.021376483142375946,\n", "    -0.011079968884587288,\n", "    -0.033708784729242325,\n", "    -0.023715320974588394,\n", "    0.02448911964893341,\n", "    -0.016064127907156944,\n", "    -0.0551966093480587,\n", "    0.001762301311828196,\n", "    -0.06529679894447327,\n", "    -0.02191687747836113,\n", "    0.09698594361543655,\n", "    0.0017255661077797413,\n", "    0.0199518371373415,\n", "    -0.06653959304094315,\n", "    -0.0409354604780674,\n", "    -0.07641920447349548,\n", "    -0.06896789371967316,\n", "    -0.032008588314056396,\n", "    0.019156785681843758,\n", "    -0.017102790996432304,\n", "    0.02606816031038761,\n", "    -0.05655210092663765,\n", "    0.016208482906222343,\n", "    0.13058845698833466,\n", "    0.03453303128480911,\n", "    -0.07962842285633087,\n", "    0.02121860533952713,\n", "    0.055431317538022995,\n", "    0.10152226686477661,\n", "    0.008103915490210056,\n", "    -0.03683888539671898,\n", "    0.02574584074318409,\n", "    0.011243137530982494,\n", "    0.10018879920244217,\n", "    0.03304858133196831,\n", "    -0.09540098905563354,\n", "    0.07041487842798233,\n", "    0.034508150070905685,\n", "    0.01460021361708641,\n", "    0.008376038633286953,\n", "    0.0038410015404224396,\n", "    0.08130525052547455,\n", "    0.04101838916540146,\n", "    0.011600143276154995,\n", "    0.007790735457092524,\n", "    0.1127326712012291,\n", "    -0.045963216572999954,\n", "    -0.0664999932050705,\n", "    -0.017671745270490646,\n", "    -0.005225293803960085,\n", "    0.05002942308783531,\n", "    0.06427975744009018,\n", "    0.030651217326521873,\n", "    -0.05309436470270157,\n", "    0.014891154132783413,\n", "    -0.018382996320724487,\n", "    0.013144832104444504,\n", "    -0.024385793134570122,\n", "    0.019964369013905525,\n", "    -0.06033214181661606,\n", "    0.0022930377162992954,\n", "    -0.003508232533931732,\n", "    -0.04073740541934967,\n", "    0.04716433212161064,\n", "    -0.046041328459978104,\n", "    0.0159757100045681,\n", "    -0.002683168975636363,\n", "    0.02553499862551689,\n", "    0.022968854755163193,\n", "    -0.03239673376083374,\n", "    0.05959976837038994,\n", "    -0.012756704352796078,\n", "    -0.024949612095952034,\n", "    -0.0016001613112166524,\n", "    0.08537652343511581,\n", "    0.02909388765692711,\n", "    0.08010498434305191,\n", "    0.044300105422735214,\n", "    -0.054590534418821335,\n", "    -0.037735335528850555,\n", "    -0.12417023628950119,\n", "    0.0020115417428314686,\n", "    7.6322666481376e-31,\n", "    -0.05822567641735077,\n", "    -0.007905213162302971,\n", "    -0.025898609310388565,\n", "    -0.07298273593187332,\n", "    -0.03224034979939461,\n", "    -0.0642695277929306,\n", "    0.10017573833465576,\n", "    0.03133203089237213,\n", "    -0.053671278059482574,\n", "    0.010978437028825283,\n", "    0.058062903583049774,\n", "    0.041533298790454865,\n", "    0.05572965368628502,\n", "    0.12009482830762863,\n", "    0.028248803690075874,\n", "    0.02767319232225418,\n", "    -0.001315077650360763,\n", "    -0.05368664488196373,\n", "    0.028004834428429604,\n", "    0.007707684300839901,\n", "    0.05650857836008072,\n", "    -0.07474973797798157,\n", "    0.000397176161641255,\n", "    -0.003210063325241208,\n", "    0.011564108543097973,\n", "    0.029501723125576973,\n", "    -0.0011932884808629751,\n", "    0.029090307652950287,\n", "    0.03340334817767143,\n", "    0.04482515901327133,\n", "    0.04406985640525818,\n", "    -0.03267647698521614,\n", "    -0.07009352743625641,\n", "    -0.04417629912495613,\n", "    0.04953700676560402,\n", "    0.03432335704565048,\n", "    -0.04361099377274513,\n", "    -0.03309027850627899,\n", "    0.09212926775217056,\n", "    -0.03364323079586029,\n", "    0.04963720962405205,\n", "    0.0017702593468129635,\n", "    -0.01505028922110796,\n", "    -0.03214616701006889,\n", "    0.07345929741859436,\n", "    -0.008692620322108269,\n", "    0.049549225717782974,\n", "    -0.022906916216015816,\n", "    0.12471276521682739,\n", "    0.029943710193037987,\n", "    0.017851585522294044,\n", "    -0.08534235507249832,\n", "    -0.08527573198080063,\n", "    -0.07643932104110718,\n", "    -0.014502519741654396,\n", "    0.021097052842378616,\n", "    0.0016752457013353705,\n", "    0.04527672380208969,\n", "    -0.005556605756282806,\n", "    -0.06559605151414871,\n", "    -0.023404814302921295,\n", "    -0.0695628747344017,\n", "    -0.08114524185657501,\n", "    0.13209766149520874,\n", "    -0.11035241931676865,\n", "    0.00049986393423751,\n", "    -0.029968708753585815,\n", "    -0.05418447405099869,\n", "    0.04428645223379135,\n", "    -0.07725030928850174,\n", "    0.014048226177692413,\n", "    -0.05733368545770645,\n", "    -0.05833304300904274,\n", "    0.059355299919843674,\n", "    0.049620576202869415,\n", "    0.04653063043951988,\n", "    0.016326457262039185,\n", "    -0.009080958552658558,\n", "    -0.07712522894144058,\n", "    -0.034988343715667725,\n", "    0.062031328678131104,\n", "    -0.002850649645552039,\n", "    -0.0621723048388958,\n", "    -0.004320010542869568,\n", "    -0.0004065715183969587,\n", "    -0.04550037160515785,\n", "    0.07830226421356201,\n", "    0.032637033611536026,\n", "    0.02001771703362465,\n", "    -0.011291168630123138,\n", "    0.02123432233929634,\n", "    0.046229008585214615,\n", "    -0.1219124048948288,\n", "    0.09125137329101562,\n", "    0.0012682488886639476,\n", "    -3.97820826641459e-33,\n", "    0.017130807042121887,\n", "    -0.005145702976733446,\n", "    0.0038994138594716787,\n", "    -0.0365087129175663,\n", "    -0.0011482933769002557,\n", "    0.04601267725229263,\n", "    0.09762287884950638,\n", "    -0.10910557210445404,\n", "    0.03215901181101799,\n", "    0.034950029104948044,\n", "    -0.012161538936197758,\n", "    -0.0005489657050929964,\n", "    0.010089922696352005,\n", "    -0.00595367606729269,\n", "    0.017252257093787193,\n", "    -0.021883610635995865,\n", "    -0.09236585348844528,\n", "    0.030166445299983025,\n", "    -0.027490705251693726,\n", "    0.009802291169762611,\n", "    -0.08292970061302185,\n", "    0.0674736350774765,\n", "    -0.004438226576894522,\n", "    -0.035770565271377563,\n", "    0.04251373931765556,\n", "    -0.08912264555692673,\n", "    -0.09501944482326508,\n", "    -0.026740584522485733,\n", "    0.006757012568414211,\n", "    0.020510781556367874,\n", "    -0.03429889306426048,\n", "    -0.04356304183602333,\n", "    -0.022517405450344086,\n", "    0.0008701262413524091,\n", "    -0.061510853469371796,\n", "    -0.03786836192011833,\n", "    0.053542040288448334,\n", "    -0.07716691493988037,\n", "    -0.0224748607724905,\n", "    0.08839096873998642,\n", "    0.05510151386260986,\n", "    -0.05714806169271469,\n", "    -0.06583043187856674,\n", "    -0.005567491520196199,\n", "    -0.07891323417425156,\n", "    0.026302596554160118,\n", "    0.03266983851790428,\n", "    0.10057349503040314,\n", "    -0.013447878882288933,\n", "    0.06700916588306427,\n", "    0.025624439120292664,\n", "    0.005535364616662264,\n", "    0.03443119302392006,\n", "    -0.07712754607200623,\n", "    0.02367102913558483,\n", "    0.07807649672031403,\n", "    0.013351851142942905,\n", "    -0.006670376285910606,\n", "    0.044456593692302704,\n", "    0.029833847656846046,\n", "    0.02868264727294445,\n", "    0.050592318177223206,\n", "    0.018577001988887787,\n", "    -0.020208999514579773,\n", "    0.02427661418914795,\n", "    -0.1128343865275383,\n", "    -0.03199524059891701,\n", "    0.1004473939538002,\n", "    -0.022203940898180008,\n", "    -0.004777332302182913,\n", "    -0.1058192327618599,\n", "    -0.002937811426818371,\n", "    -0.022396542131900787,\n", "    -0.10088841617107391,\n", "    -0.012279574759304523,\n", "    0.06898317486047745,\n", "    0.010226346552371979,\n", "    0.027393346652388573,\n", "    -0.0011895119678229094,\n", "    -0.08612816780805588,\n", "    -0.07133139669895172,\n", "    -0.008479105308651924,\n", "    -0.031838346272706985,\n", "    -0.008334285579621792,\n", "    0.08622665703296661,\n", "    0.047420624643564224,\n", "    0.021585781127214432,\n", "    -0.06033699959516525,\n", "    0.0013718903064727783,\n", "    0.0489850752055645,\n", "    0.043618619441986084,\n", "    -0.015482702292501926,\n", "    0.045276474207639694,\n", "    0.016611196100711823,\n", "    -0.02924053557217121,\n", "    -6.120698065685877e-33,\n", "    0.00506728608161211,\n", "    -0.001903363736346364,\n", "    0.0447794571518898,\n", "    -0.01693131774663925,\n", "    0.014036769047379494,\n", "    -0.041077498346567154,\n", "    0.004954314325004816,\n", "    0.065757617354393,\n", "    0.012945794500410557,\n", "    0.1058562621474266,\n", "    0.027927296236157417,\n", "    -0.0645192563533783,\n", "    -0.07165123522281647,\n", "    0.021113427355885506,\n", "    0.03293512761592865,\n", "    0.010322004556655884,\n", "    0.09938368201255798,\n", "    -0.02350602112710476,\n", "    -0.017255399376153946,\n", "    0.02132754772901535,\n", "    -0.03500659018754959,\n", "    0.001496687880717218,\n", "    -0.15367893874645233,\n", "    -0.028192074969410896,\n", "    0.02141306735575199,\n", "    -0.00011361677024979144,\n", "    0.09003408253192902,\n", "    0.10600052773952484,\n", "    -0.03688175603747368,\n", "    0.016184408217668533,\n", "    0.017232947051525116,\n", "    0.07220735400915146,\n", "    0.03207571804523468,\n", "    -0.05977824702858925,\n", "    0.012391328811645508,\n", "    0.020289238542318344,\n", "    -0.007679024711251259,\n", "    -0.0035240785218775272,\n", "    0.013295073062181473,\n", "    -0.006690528709441423,\n", "    -0.014766011387109756,\n", "    0.11739892512559891,\n", "    0.08444827049970627,\n", "    0.01220651064068079,\n", "    -0.06881215423345566,\n", "    0.02970905788242817,\n", "    -0.08319439738988876,\n", "    -0.0643107146024704,\n", "    -0.015392026863992214,\n", "    -0.04502538591623306,\n", "    -0.02080647274851799,\n", "    -0.07299415022134781,\n", "    0.05707911029458046,\n", "    -0.08277551829814911,\n", "    0.06983409821987152,\n", "    0.08155222237110138,\n", "    0.08553817123174667,\n", "    -0.017201576381921768,\n", "    0.008726004511117935,\n", "    -0.004336347803473473,\n", "    -0.008869408629834652,\n", "    0.03283241391181946,\n", "    0.045553337782621384,\n", "    -0.05067482218146324],\n", "   'question_text_vector': [-0.08970632404088974,\n", "    -0.03653658181428909,\n", "    0.030594786629080772,\n", "    0.033773597329854965,\n", "    0.07841767370700836,\n", "    0.01642126403748989,\n", "    0.02012832649052143,\n", "    0.011863150633871555,\n", "    -0.010526211932301521,\n", "    -0.021763769909739494,\n", "    -0.044110141694545746,\n", "    -0.04223739355802536,\n", "    0.019572241231799126,\n", "    0.011417457833886147,\n", "    -0.039079178124666214,\n", "    0.022197410464286804,\n", "    0.04858148470520973,\n", "    -0.14810223877429962,\n", "    0.06247995048761368,\n", "    -0.057603009045124054,\n", "    -0.07422260195016861,\n", "    -0.060525525361299515,\n", "    0.0010091696167364717,\n", "    -0.019257940351963043,\n", "    0.010677410289645195,\n", "    -0.008529117330908775,\n", "    0.09573714435100555,\n", "    0.024567991495132446,\n", "    -0.007886013016104698,\n", "    0.018684981390833855,\n", "    -0.031260065734386444,\n", "    -0.021496741101145744,\n", "    0.11325468122959137,\n", "    0.03824090585112572,\n", "    0.09079346805810928,\n", "    0.01742415316402912,\n", "    0.044800274074077606,\n", "    -0.041796471923589706,\n", "    0.012971987016499043,\n", "    -0.038886379450559616,\n", "    -0.06779869645833969,\n", "    -0.09482178092002869,\n", "    0.003860021475702524,\n", "    0.052276719361543655,\n", "    0.0411318838596344,\n", "    0.02034034952521324,\n", "    0.020014991983771324,\n", "    -0.021688055247068405,\n", "    -0.07249266654253006,\n", "    0.03075847215950489,\n", "    0.0023843194358050823,\n", "    -0.08362176269292831,\n", "    0.04527682438492775,\n", "    -0.06407389044761658,\n", "    -0.029468843713402748,\n", "    0.09426634013652802,\n", "    0.011585974134504795,\n", "    -0.016354531049728394,\n", "    -0.04729035496711731,\n", "    0.024815702810883522,\n", "    -0.05509418621659279,\n", "    -0.0880112275481224,\n", "    -0.05282725766301155,\n", "    -0.0031534996815025806,\n", "    -0.03120601177215576,\n", "    -0.017544720321893692,\n", "    -0.06227334216237068,\n", "    0.005532218609005213,\n", "    0.11665118485689163,\n", "    -0.11520165205001831,\n", "    -0.00999797135591507,\n", "    -0.00761615252122283,\n", "    0.04379814863204956,\n", "    0.10512111335992813,\n", "    0.035654544830322266,\n", "    0.021730611100792885,\n", "    0.04512103646993637,\n", "    0.06411053240299225,\n", "    0.08690302819013596,\n", "    0.008827097713947296,\n", "    -0.1335320770740509,\n", "    0.08597308397293091,\n", "    0.005791373550891876,\n", "    -0.025936873629689217,\n", "    0.03271471709012985,\n", "    0.0435955785214901,\n", "    0.09625934809446335,\n", "    0.13834387063980103,\n", "    -0.008043717592954636,\n", "    0.03485814854502678,\n", "    0.09693069010972977,\n", "    -0.0018631030106917024,\n", "    -0.10518543422222137,\n", "    -0.01674223504960537,\n", "    -0.04092782735824585,\n", "    0.12579844892024994,\n", "    -0.026732128113508224,\n", "    -0.03027416579425335,\n", "    -0.01946284994482994,\n", "    -0.05769338458776474,\n", "    0.004876984748989344,\n", "    0.0746903344988823,\n", "    -0.0250895693898201,\n", "    -0.0552646704018116,\n", "    -0.07305840402841568,\n", "    -0.008444387465715408,\n", "    -0.026008514687418938,\n", "    -0.04581809043884277,\n", "    0.010165979154407978,\n", "    -0.02623104676604271,\n", "    0.036193832755088806,\n", "    0.011888260953128338,\n", "    0.04658452421426773,\n", "    0.04974988102912903,\n", "    -0.05663358420133591,\n", "    0.07708363980054855,\n", "    -0.0677751675248146,\n", "    0.08315546810626984,\n", "    0.030952230095863342,\n", "    0.020631195977330208,\n", "    -0.05093248561024666,\n", "    0.021045221015810966,\n", "    0.07982803881168365,\n", "    -0.08717453479766846,\n", "    -0.0035442286171019077,\n", "    -0.1573793590068817,\n", "    -0.01892615482211113,\n", "    3.1619041848559405e-31,\n", "    -0.034328848123550415,\n", "    -0.04303446039557457,\n", "    -0.0476088747382164,\n", "    -0.06322967261075974,\n", "    -0.012356719002127647,\n", "    -0.12283490598201752,\n", "    0.08874236792325974,\n", "    -0.002813486848026514,\n", "    -0.020542850717902184,\n", "    0.012772477231919765,\n", "    -0.04177570343017578,\n", "    0.042019058018922806,\n", "    0.06667157262563705,\n", "    0.012941875495016575,\n", "    -0.032045986503362656,\n", "    0.019192006438970566,\n", "    -0.10804585367441177,\n", "    -0.03091624192893505,\n", "    -0.055116280913352966,\n", "    0.024736259132623672,\n", "    0.04910832643508911,\n", "    -0.03309693560004234,\n", "    0.027917150408029556,\n", "    -0.03587581217288971,\n", "    0.05874478444457054,\n", "    0.0012470324290916324,\n", "    -0.0281706303358078,\n", "    0.11089640110731125,\n", "    0.010819920338690281,\n", "    0.042825378477573395,\n", "    0.009977200999855995,\n", "    -0.020958345383405685,\n", "    0.01041414774954319,\n", "    -0.00529916537925601,\n", "    0.03445708006620407,\n", "    0.027542874217033386,\n", "    0.004130349028855562,\n", "    0.011657544411718845,\n", "    0.044226326048374176,\n", "    0.017984481528401375,\n", "    0.05289424955844879,\n", "    -0.0021149085368961096,\n", "    -0.03057926893234253,\n", "    -0.0450507216155529,\n", "    0.04870574548840523,\n", "    -0.006583108566701412,\n", "    0.08126228302717209,\n", "    -0.0685296356678009,\n", "    0.18330076336860657,\n", "    -0.04823942482471466,\n", "    -0.015360691584646702,\n", "    -0.036734599620103836,\n", "    -0.014262747950851917,\n", "    -0.04818180203437805,\n", "    -0.0037285778671503067,\n", "    -0.002120065502822399,\n", "    0.05819755047559738,\n", "    0.05380557104945183,\n", "    -0.014478757046163082,\n", "    0.030121946707367897,\n", "    -0.04207455366849899,\n", "    -0.04318953678011894,\n", "    0.0027894084341824055,\n", "    0.05024218186736107,\n", "    -0.06945369392633438,\n", "    -0.06474371254444122,\n", "    -0.030659694224596024,\n", "    -0.008934061974287033,\n", "    -0.009190717712044716,\n", "    0.006906142458319664,\n", "    0.04701068624854088,\n", "    -0.05824621766805649,\n", "    -0.025760728865861893,\n", "    -0.023170029744505882,\n", "    0.06988601386547089,\n", "    0.006705363281071186,\n", "    0.012385724112391472,\n", "    0.014579803682863712,\n", "    -0.0671776682138443,\n", "    0.004674227442592382,\n", "    0.033874280750751495,\n", "    0.0064709545113146305,\n", "    -0.06729737669229507,\n", "    -0.0013047578977420926,\n", "    0.03387445583939552,\n", "    0.037173446267843246,\n", "    0.09549836814403534,\n", "    0.04030503332614899,\n", "    -0.02501581981778145,\n", "    0.026046819984912872,\n", "    -0.059767309576272964,\n", "    -0.00229269964620471,\n", "    -0.07869169116020203,\n", "    0.07099837064743042,\n", "    0.03503889963030815,\n", "    -4.187228794406055e-33,\n", "    0.037900421768426895,\n", "    0.012306852266192436,\n", "    0.02480163425207138,\n", "    -0.07484794408082962,\n", "    0.010142482817173004,\n", "    0.020406881347298622,\n", "    0.1005338802933693,\n", "    -0.011784318834543228,\n", "    -0.035317860543727875,\n", "    0.038894616067409515,\n", "    0.06777768582105637,\n", "    0.01250914204865694,\n", "    -0.02727668173611164,\n", "    0.008859190158545971,\n", "    -0.04218030348420143,\n", "    -0.030515950173139572,\n", "    -0.011957762762904167,\n", "    0.021706465631723404,\n", "    0.017461834475398064,\n", "    0.018970375880599022,\n", "    -0.039142511785030365,\n", "    -0.014545444399118423,\n", "    -0.08633864670991898,\n", "    -0.04265381768345833,\n", "    0.021341929212212563,\n", "    -0.07484499365091324,\n", "    -0.014727792702615261,\n", "    0.05996964871883392,\n", "    -0.004642661195248365,\n", "    0.002628076821565628,\n", "    0.0016010710969567299,\n", "    -0.022302791476249695,\n", "    0.014996531419456005,\n", "    0.07680269330739975,\n", "    0.02943757362663746,\n", "    -0.04638088122010231,\n", "    0.06343329697847366,\n", "    -0.0943354144692421,\n", "    0.04351941496133804,\n", "    0.06513310968875885,\n", "    0.09839755296707153,\n", "    -0.011934666894376278,\n", "    -0.0748915821313858,\n", "    -0.006236914079636335,\n", "    -0.09336396306753159,\n", "    -0.04345177114009857,\n", "    -0.0058838664554059505,\n", "    0.055118005722761154,\n", "    -0.016665251925587654,\n", "    0.03576120361685753,\n", "    -0.0024883390869945288,\n", "    0.03363918140530586,\n", "    0.06708067655563354,\n", "    -0.02753029391169548,\n", "    -0.010084708221256733,\n", "    0.048580512404441833,\n", "    -0.03246774896979332,\n", "    -0.012320036068558693,\n", "    0.04279026761651039,\n", "    0.05233009532094002,\n", "    -0.024494031444191933,\n", "    0.05303075164556503,\n", "    0.12649616599082947,\n", "    -0.06866542994976044,\n", "    0.005785221233963966,\n", "    -0.07833477109670639,\n", "    0.0012940625892952085,\n", "    0.07623729854822159,\n", "    -0.02420567162334919,\n", "    0.04136747121810913,\n", "    -0.040481843054294586,\n", "    -0.013298741541802883,\n", "    -0.09372876584529877,\n", "    -0.04312615469098091,\n", "    0.018170425668358803,\n", "    0.011880318634212017,\n", "    0.0017531232442706823,\n", "    -0.03179362788796425,\n", "    -0.05282887443900108,\n", "    -0.06134025380015373,\n", "    -0.014599276706576347,\n", "    0.00851496309041977,\n", "    -0.04609876126050949,\n", "    0.04313698783516884,\n", "    0.07381999492645264,\n", "    0.07765170931816101,\n", "    0.0018207286484539509,\n", "    0.0012974296696484089,\n", "    0.09123604744672775,\n", "    -0.006329046096652746,\n", "    0.05713903531432152,\n", "    -0.06346751004457474,\n", "    0.021337253972887993,\n", "    0.020643534138798714,\n", "    -0.07072151452302933,\n", "    -4.41106128590185e-33,\n", "    0.00047660607378929853,\n", "    -0.020899051800370216,\n", "    0.07973586022853851,\n", "    0.05646766722202301,\n", "    -0.0072766197845339775,\n", "    0.0206907968968153,\n", "    -0.04398543760180473,\n", "    0.011072726920247078,\n", "    -0.011190813966095448,\n", "    0.02080981433391571,\n", "    0.039939314126968384,\n", "    -0.09016377478837967,\n", "    -0.0446629524230957,\n", "    -0.027285335585474968,\n", "    0.08258123695850372,\n", "    0.01029777992516756,\n", "    0.000151514817844145,\n", "    -0.02362971380352974,\n", "    0.01609489880502224,\n", "    -0.055767204612493515,\n", "    -0.028492312878370285,\n", "    -0.015431826002895832,\n", "    -0.054363809525966644,\n", "    -0.016051191836595535,\n", "    0.053995124995708466,\n", "    0.055379968136548996,\n", "    0.039840005338191986,\n", "    0.045173175632953644,\n", "    -0.10705079883337021,\n", "    -0.04998397082090378,\n", "    -0.014069030061364174,\n", "    -0.004976013209670782,\n", "    0.016995247453451157,\n", "    -0.028811311349272728,\n", "    0.026075564324855804,\n", "    0.024125825613737106,\n", "    0.0191927719861269,\n", "    0.005659198388457298,\n", "    0.009844842366874218,\n", "    -0.03452266752719879,\n", "    -0.0004498532507568598,\n", "    0.09333029389381409,\n", "    0.0484379343688488,\n", "    -0.002345471875742078,\n", "    -0.07435981929302216,\n", "    0.05822377651929855,\n", "    -0.10094793885946274,\n", "    -0.054983723908662796,\n", "    0.016803473234176636,\n", "    -0.029692083597183228,\n", "    0.0009698471403680742,\n", "    -0.014921888709068298,\n", "    -0.004571937955915928,\n", "    -0.06707686185836792,\n", "    0.04045085981488228,\n", "    0.09394976496696472,\n", "    -0.004803560208529234,\n", "    -0.0273368488997221,\n", "    0.029812311753630638,\n", "    0.031071221455931664,\n", "    -0.037169575691223145,\n", "    0.028585871681571007,\n", "    -0.027297912165522575,\n", "    -0.08384589850902557]}}]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["response[\"hits\"][\"hits\"]"]}, {"cell_type": "markdown", "id": "3e805440-06c7-48e9-8a8d-feba38ace95f", "metadata": {}, "source": ["## Hybrid search pipeline"]}, {"cell_type": "code", "execution_count": 25, "id": "6b7e625d-ccb3-4597-aefc-022137fc33b8", "metadata": {}, "outputs": [], "source": ["df_ground_truth = pd.read_csv('ground-truth-data.csv')"]}, {"cell_type": "code", "execution_count": 26, "id": "94fc36e5-7141-4a19-a445-a22a63c68471", "metadata": {}, "outputs": [], "source": ["ground_truth = df_ground_truth.to_dict(orient='records')"]}, {"cell_type": "code", "execution_count": 27, "id": "8bd59d82-6e4f-48b2-95ee-81031d16594b", "metadata": {}, "outputs": [], "source": ["def hit_rate(relevance_total):\n", "    cnt = 0\n", "\n", "    for line in relevance_total:\n", "        if True in line:\n", "            cnt = cnt + 1\n", "\n", "    return cnt / len(relevance_total)"]}, {"cell_type": "code", "execution_count": 28, "id": "c3d577bb-f890-483e-9afa-580411750dbb", "metadata": {}, "outputs": [], "source": ["def mrr(relevance_total):\n", "    total_score = 0.0\n", "\n", "    for line in relevance_total:\n", "        for rank in range(len(line)):\n", "            if line[rank] == True:\n", "                total_score = total_score + 1 / (rank + 1)\n", "\n", "    return total_score / len(relevance_total)"]}, {"cell_type": "code", "execution_count": 44, "id": "046a7155-875b-45aa-9889-cd3410f5e86b", "metadata": {}, "outputs": [], "source": ["def elastic_search_hybrid(field, query, vector, course):\n", "    knn_query = {\n", "        \"field\": field,\n", "        \"query_vector\": vector,\n", "        \"k\": 5,\n", "        \"num_candidates\": 10000,\n", "        \"boost\": 0.5,\n", "        \"filter\": {\n", "            \"term\": {\n", "                \"course\": course\n", "            }\n", "        }\n", "    }\n", "\n", "    keyword_query = {\n", "        \"bool\": {\n", "            \"must\": {\n", "                \"multi_match\": {\n", "                    \"query\": query,\n", "                    \"fields\": [\"question\", \"text\", \"section\"],\n", "                    \"type\": \"best_fields\",\n", "                    \"boost\": 0.5,\n", "                }\n", "            },\n", "            \"filter\": {\n", "                \"term\": {\n", "                    \"course\": course\n", "                }\n", "            }\n", "        }\n", "    }\n", "\n", "    search_query = {\n", "        \"knn\": knn_query,\n", "        \"query\": keyword_query,\n", "        \"size\": 5,\n", "        \"_source\": [\"text\", \"section\", \"question\", \"course\", \"id\"]\n", "    }\n", "\n", "    es_results = es_client.search(\n", "        index=index_name,\n", "        body=search_query\n", "    )\n", "    \n", "    result_docs = []\n", "    \n", "    for hit in es_results['hits']['hits']:\n", "        result_docs.append(hit['_source'])\n", "\n", "    return result_docs"]}, {"cell_type": "code", "execution_count": 49, "id": "88edfdf8-36db-4038-a259-dc1496ceedcc", "metadata": {}, "outputs": [], "source": ["def question_hybrid(q):\n", "    question = q['question']\n", "    course = q['course']\n", "\n", "    v_q = model.encode(question)\n", "\n", "    return elastic_search_hybrid('question_vector', question, v_q, course)"]}, {"cell_type": "code", "execution_count": 23, "id": "231738e6-fb84-42f6-bc28-f4cbbdcfabdb", "metadata": {}, "outputs": [], "source": ["def evaluate(ground_truth, search_function):\n", "    relevance_total = []\n", "\n", "    for q in tqdm(ground_truth):\n", "        doc_id = q['document']\n", "        results = search_function(q)\n", "        relevance = [d['id'] == doc_id for d in results]\n", "        relevance_total.append(relevance)\n", "\n", "    return {\n", "        'hit_rate': hit_rate(relevance_total),\n", "        'mrr': mrr(relevance_total),\n", "    }"]}, {"cell_type": "code", "execution_count": 51, "id": "497b5be2-56c0-4435-9c08-d765c1db504b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4627/4627 [02:00<00:00, 38.35it/s]\n"]}, {"data": {"text/plain": ["{'hit_rate': 0.9234925437648585, 'mrr': 0.8481665586052878}"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["evaluate(ground_truth, question_hybrid)"]}, {"cell_type": "markdown", "id": "7016f3b9-872a-48e2-8cbb-56bfd605f7e4", "metadata": {}, "source": ["ES knn on questions: `{'hit_rate': 0.773071104387292, 'mrr': 0.6666810748505158}`"]}, {"cell_type": "code", "execution_count": 52, "id": "b6ecc8b0-44ad-4b63-9cea-12150becbb57", "metadata": {}, "outputs": [], "source": ["def text_hybrid(q):\n", "    question = q['question']\n", "    course = q['course']\n", "\n", "    v_q = model.encode(question)\n", "\n", "    return elastic_search_hybrid('text_vector', question, v_q, course)"]}, {"cell_type": "code", "execution_count": 53, "id": "85d27dc8-0616-4b12-aa95-58d8d9603361", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4627/4627 [01:51<00:00, 41.43it/s]\n"]}, {"data": {"text/plain": ["{'hit_rate': 0.9234925437648585, 'mrr': 0.8461710251422809}"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["evaluate(ground_truth, text_hybrid)"]}, {"cell_type": "markdown", "id": "925e73fa-131f-4ef0-8fe4-fb03f1ef8b25", "metadata": {}, "source": ["ES knn on texts: `{'hit_rate': 0.8286146531229739, 'mrr': 0.7062315395144454}`"]}, {"cell_type": "code", "execution_count": 54, "id": "d0687c84-54b0-4b29-af77-dba6e6c51617", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4627/4627 [01:48<00:00, 42.46it/s]\n"]}, {"data": {"text/plain": ["{'hit_rate': 0.9250054030689432, 'mrr': 0.8506231539514445}"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["def question_text_hybrid(q):\n", "    question = q['question']\n", "    course = q['course']\n", "\n", "    v_q = model.encode(question)\n", "\n", "    return elastic_search_hybrid('question_text_vector', question, v_q, course)\n", "\n", "evaluate(ground_truth, question_text_hybrid)"]}, {"cell_type": "markdown", "id": "67782a18-cfac-40c0-8681-5d345ac1c30d", "metadata": {}, "source": ["ES knn on questions and answers: `{'hit_rate': 0.9172249837907932, 'mrr': 0.824306606152295}`"]}, {"cell_type": "markdown", "id": "c3ffedb0-3259-46c0-bf75-e955a76a9790", "metadata": {}, "source": ["## Reranking"]}, {"cell_type": "markdown", "id": "9bb0e1b5-9ee9-458e-9c3f-f06c5916103c", "metadata": {}, "source": ["To use the Reciprocal rank fusion (RRF) score we need to pull the docker image with a more recent version of Elasticsearch:\n", "```bash\n", "docker run -it \\\n", "    --rm \\\n", "    --name elasticsearch \\\n", "    -m 4GB \\\n", "    -p 9200:9200 \\\n", "    -p 9300:9300 \\\n", "    -e \"discovery.type=single-node\" \\\n", "    -e \"xpack.security.enabled=false\" \\\n", "    docker.elastic.co/elasticsearch/elasticsearch:8.9.0\n", "```"]}, {"cell_type": "code", "execution_count": 16, "id": "0d878d5a-b15a-4459-a6f0-5174af6d1bdb", "metadata": {}, "outputs": [], "source": ["def elastic_search_hybrid_rrf(field, query, vector, course):\n", "    knn_query = {\n", "        \"field\": field,\n", "        \"query_vector\": vector,\n", "        \"k\": 5,\n", "        \"num_candidates\": 10000,\n", "        \"boost\": 0.5,\n", "        \"filter\": {\n", "            \"term\": {\n", "                \"course\": course\n", "            }\n", "        }\n", "    }\n", "\n", "    keyword_query = {\n", "        \"bool\": {\n", "            \"must\": {\n", "                \"multi_match\": {\n", "                    \"query\": query,\n", "                    \"fields\": [\"question\", \"text\", \"section\"],\n", "                    \"type\": \"best_fields\",\n", "                    \"boost\": 0.5,\n", "                }\n", "            },\n", "            \"filter\": {\n", "                \"term\": {\n", "                    \"course\": course\n", "                }\n", "            }\n", "        }\n", "    }\n", "\n", "    search_query = {\n", "        \"knn\": knn_query,\n", "        \"query\": keyword_query,\n", "        \"size\": 5,\n", "        \"rank\": {\n", "            \"rrf\": {}\n", "        },\n", "        \"_source\": [\"text\", \"section\", \"question\", \"course\", \"id\"]\n", "    }\n", "\n", "    es_results = es_client.search(\n", "        index=index_name,\n", "        body=search_query\n", "    )\n", "    \n", "    result_docs = []\n", "    \n", "    for hit in es_results['hits']['hits']:\n", "        result_docs.append(hit['_source'])\n", "\n", "    return result_docs"]}, {"cell_type": "code", "execution_count": 17, "id": "78c650ec-6e76-4838-a2b0-2902bfd7c922", "metadata": {}, "outputs": [], "source": ["course = \"data-engineering-zoomcamp\""]}, {"cell_type": "code", "execution_count": 18, "id": "bc9dfdaf-c10e-4470-8687-543a566a222f", "metadata": {}, "outputs": [], "source": ["query = 'I just discovered the course. Can I still join it?'"]}, {"cell_type": "code", "execution_count": 19, "id": "a13b975b-d21b-4ad9-9756-7771319bec83", "metadata": {}, "outputs": [], "source": ["v_q = model.encode(query)"]}, {"cell_type": "code", "execution_count": null, "id": "ce90bf90-8453-491e-8d9e-eee20c1c42b4", "metadata": {}, "outputs": [], "source": ["elastic_search_hybrid_rrf('question_text_vector', query, v_q, course)"]}, {"cell_type": "markdown", "id": "8265dd67-a9c5-4e0b-9c45-9b7326fc4b74", "metadata": {}, "source": ["By default, RRF isn't available in a free-tier subscription. But you can try to use 30-day trial or upgrade the subscription plan."]}, {"cell_type": "markdown", "id": "2a02f804-1d54-45f5-8351-eadb194daa38", "metadata": {}, "source": ["### RRF implementation"]}, {"cell_type": "code", "execution_count": 30, "id": "342a7a58-890f-4439-9fb2-a9037185058a", "metadata": {}, "outputs": [], "source": ["def compute_rrf(rank, k=60):\n", "    \"\"\" Our own implementation of the relevance score \"\"\"\n", "    return 1 / (k + rank)\n", "\n", "def elastic_search_hybrid_rrf(field, query, vector, course, k=60):\n", "    knn_query = {\n", "        \"field\": field,\n", "        \"query_vector\": vector,\n", "        \"k\": 10,\n", "        \"num_candidates\": 10000,\n", "        \"boost\": 0.5,\n", "        \"filter\": {\n", "            \"term\": {\n", "                \"course\": course\n", "            }\n", "        }\n", "    }\n", "\n", "    keyword_query = {\n", "        \"bool\": {\n", "            \"must\": {\n", "                \"multi_match\": {\n", "                    \"query\": query,\n", "                    \"fields\": [\"question\", \"text\", \"section\"],\n", "                    \"type\": \"best_fields\",\n", "                    \"boost\": 0.5,\n", "                }\n", "            },\n", "            \"filter\": {\n", "                \"term\": {\n", "                    \"course\": course\n", "                }\n", "            }\n", "        }\n", "    }\n", "\n", "    knn_results = es_client.search(\n", "        index=index_name, \n", "        body={\n", "            \"knn\": knn_query, \n", "            \"size\": 10\n", "        }\n", "    )['hits']['hits']\n", "    \n", "    keyword_results = es_client.search(\n", "        index=index_name, \n", "        body={\n", "            \"query\": keyword_query, \n", "            \"size\": 10\n", "        }\n", "    )['hits']['hits']\n", "    \n", "    rrf_scores = {}\n", "    # Calculate RRF using vector search results\n", "    for rank, hit in enumerate(knn_results):\n", "        doc_id = hit['_id']\n", "        rrf_scores[doc_id] = compute_rrf(rank + 1, k)\n", "\n", "    # Adding keyword search result scores\n", "    for rank, hit in enumerate(keyword_results):\n", "        doc_id = hit['_id']\n", "        if doc_id in rrf_scores:\n", "            rrf_scores[doc_id] += compute_rrf(rank + 1, k)\n", "        else:\n", "            rrf_scores[doc_id] = compute_rrf(rank + 1, k)\n", "\n", "    # Sort RRF scores in descending order\n", "    reranked_docs = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)\n", "    \n", "    # Get top-K documents by the score\n", "    final_results = []\n", "    for doc_id, score in reranked_docs[:5]:\n", "        doc = es_client.get(index=index_name, id=doc_id)\n", "        final_results.append(doc['_source'])\n", "    \n", "    return final_results"]}, {"cell_type": "code", "execution_count": 29, "id": "ac3a817b-0c7a-4e48-8a6f-2a9a2c67a214", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4627/4627 [03:08<00:00, 24.49it/s]\n"]}, {"data": {"text/plain": ["{'hit_rate': 0.9520207477847418, 'mrr': 0.8745911677833017}"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["def question_text_hybrid_rrf(q):\n", "    question = q['question']\n", "    course = q['course']\n", "\n", "    v_q = model.encode(question)\n", "\n", "    return elastic_search_hybrid_rrf('question_text_vector', question, v_q, course)\n", "\n", "evaluate(ground_truth, question_text_hybrid_rrf)"]}, {"cell_type": "markdown", "id": "46a1c0de-8d15-4cc0-b777-b0590e16f3ef", "metadata": {}, "source": ["ES hybrid search scores: `{'hit_rate': 0.9250054030689432, 'mrr': 0.8506231539514445}`"]}, {"cell_type": "code", "execution_count": null, "id": "7eefbc39-fe46-42f7-9507-36e9b3cd897e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}