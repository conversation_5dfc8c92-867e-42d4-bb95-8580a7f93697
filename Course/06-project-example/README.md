# 7. End-to-End Project Example 

Links:

* [Project alexeygrigorev/fitness-assistant](https://github.com/alexeygrigorev/fitness-assistant)
* [Project criteria](../project.md#evaluation-criteria)


Note: check the final result, it's a bit different 
from what we showed in the videos: we further improved it
by doing some small things here and there, like improved
README, code readability, etc.


## 7.1. Fitness assistant project

<a href="https://www.youtube.com/watch?v=E9O0Tg68PPg&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/E9O0Tg68PPg">
</a>

* Generating data for the project
* Setting up the project
* Implementing the initial version of the RAG flow

## 7.2. Evaluating retrieval

<a href="https://www.youtube.com/watch?v=6ulnHtJPCWY&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/6ulnHtJPCWY">
</a>

* Preparing the README file
* Generating gold standard evaluation data
* Evaluting retrieval
* Findning the best boosting coefficients


## 7.3 Evaluating RAG

<a href="https://www.youtube.com/watch?v=lxpW2mR7dGk&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/lxpW2mR7dGk">
</a>

* Using LLM-as-a-Judge (type 2)
* Comparing gpt-4o-mini with gpt-4o

## 7.4 Interface and ingestion pipeline

<a href="https://www.youtube.com/watch?v=vMHve2EyA5M&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/vMHve2EyA5M">
</a>

* Turnining the jupyter notebook into a script
* Creating the ingestion pipeline
* Creating the API interface with Flask
* Improving README


## 7.5 Monitoring and containerization

<a href="https://www.youtube.com/watch?v=nQda9etJWW8&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/nQda9etJWW8">
</a>

* Creating a Docker image for our application
* Putting everything in docker compose
* Logging all the information for monitoring purposes


## 7.6 Summary and closing remarks

<a href="https://www.youtube.com/watch?v=TW9M5VE8vpo&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/TW9M5VE8vpo">
</a>

* Changes between 7.5 and 7.6 (postres logging, grafara, cli.py, etc)
* README file improvements
* Total cost of the project (~$2) and how to lower it
* Using generated data for real-life projects


## 7.7 Chunking for longer texts

<a href="https://www.youtube.com/watch?v=tyBRP_WewXA&list=PL3MmuxUbc_hIB4fSqLy_0AfTjVLpgjV3R">
  <img src="https://markdown-videos-api.jorgenkh.no/youtube/tyBRP_WewXA">
</a>

* Different chunking strategies
* [Use cases: multiple articles, one article, slide decks](content-processing-summary.md)

Links:

* https://chatgpt.com/share/a4616f6b-43f4-4225-9d03-bb69c723c210
* https://chatgpt.com/share/74217c02-95e6-46ae-b5a5-ca79f9a07084
* https://chatgpt.com/share/8cf0ebde-c53f-4c6f-82ae-c6cc52b2fd0b

# Notes

* First link goes here
* Did you take notes? Add them above this line (Send a PR with *links* to your notes)
