## Homework: Search Evaluation

In this homework, we will evaluate the results of vector
search.

> It's possible that your answers won't match exactly. If it's the case, select the closest one.


## Required libraries

We will use minsearch and Qdrant. Make sure you have the most up-to-date versions:

```bash
pip install -U minsearch qdrant_client
``` 

minsearch should be at least 0.0.4.



## Evaluation data

For this homework, we will use the same dataset we generated
in the videos.

Let's get them:

```python
import requests
import pandas as pd

url_prefix = 'https://raw.githubusercontent.com/DataTalksClub/llm-zoomcamp/main/03-evaluation/'
docs_url = url_prefix + 'search_evaluation/documents-with-ids.json'
documents = requests.get(docs_url).json()

ground_truth_url = url_prefix + 'search_evaluation/ground-truth-data.csv'
df_ground_truth = pd.read_csv(ground_truth_url)
ground_truth = df_ground_truth.to_dict(orient='records')
```

Here, `documents` contains the documents from the FAQ database
with unique IDs, and `ground_truth` contains generated
question-answer pairs. 

Also, we will need the code for evaluating retrieval:

```python
from tqdm.auto import tqdm

def hit_rate(relevance_total):
    cnt = 0

    for line in relevance_total:
        if True in line:
            cnt = cnt + 1

    return cnt / len(relevance_total)

def mrr(relevance_total):
    total_score = 0.0

    for line in relevance_total:
        for rank in range(len(line)):
            if line[rank] == True:
                total_score = total_score + 1 / (rank + 1)

    return total_score / len(relevance_total)

def evaluate(ground_truth, search_function):
    relevance_total = []

    for q in tqdm(ground_truth):
        doc_id = q['document']
        results = search_function(q)
        relevance = [d['id'] == doc_id for d in results]
        relevance_total.append(relevance)

    return {
        'hit_rate': hit_rate(relevance_total),
        'mrr': mrr(relevance_total),
    }
```

## Q1. Minsearch text

Now let's evaluate our usual minsearch approach, indexing documents with:
```python
text_fields=["question", "section", "text"],
keyword_fields=["course", "id"]
```
but tweak the parameters for search. Let's use the following boosting params:

```python
boost = {'question': 1.5, 'section': 0.1}
```

What's the hitrate for this approach?

* 0.64
* 0.74
* 0.84
* 0.94


## Embeddings 

The latest version of minsearch also supports vector search. 
We will use it:

```python
from minsearch import VectorSearch
```

We will also use TF-IDF and Singular Value Decomposition to 
create embeddings from texts. You can refer to our
["Create Your Own Search Engine" workshop](https://github.com/alexeygrigorev/build-your-own-search-engine)
if you want to know more about it.

```python
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.decomposition import TruncatedSVD
from sklearn.pipeline import make_pipeline
```

Let's create embeddings for the "question" field:

```python
texts = []

for doc in documents:
    t = doc['question']
    texts.append(t)

pipeline = make_pipeline(
    TfidfVectorizer(min_df=3),
    TruncatedSVD(n_components=128, random_state=1)
)
X = pipeline.fit_transform(texts)
```

## Q2. Vector search for question

Now let's index these embeddings with minsearch:

```python
vindex = VectorSearch(keyword_fields={'course'})
vindex.fit(X, documents)
```

Evaluate this seach method. What's MRR for it?

- 0.25
- 0.35
- 0.45
- 0.55

## Q3. Vector search for question and answer

We only used question in Q2. We can use both question and answer:

```python
texts = []

for doc in documents:
    t = doc['question'] + ' ' + doc['text']
    texts.append(t)
```

Using the same pipeline (`min_df=3 for TF-IDF vectorizer and `n_components=128` for SVD), evaluate the performance of this
approach

What's the hitrate?

- 0.62
- 0.72
- 0.82
- 0.92

## Q4. Qdrant

Now let's evaluate the following settings in Qdrant:

- `text = doc['question'] + ' ' + doc['text']`
- `model_handle = "jinaai/jina-embeddings-v2-small-en"`
- `limit = 5`

What's the MRR?

- 0.65
- 0.75
- 0.85
- 0.95

## Q5. Cosine simiarity

In the second part of the module, we looked at evaluating
the entire RAG approach. In particular, we looked at 
comparing the answer generated by our system with the actual
answer from the FAQ.

One of the ways of doing it is using the cosine similarity. 
Let's see how to calculate it.

Cosine similarity is a dot product between two normalized vectors.
In geometrical sense, it's the cosine of the angle between
the vectors. Look up "cosine similarity geometry" if you want to
learn more about it.

For us, it means that we need two things:

- First, we normalize each of the vectors
- Then, compute the dot product

So, we get this:

```python
def cosine(u, v):
    u = normalize(u)
    v = normalize(v)
    return u.dot(v)
```

For normalization, we first compute the vector norm (its length),
and then divide the vector by it:

```python
def normalize(u):
    norm = np.sqrt(u.dot(u))
    return u / norm
```

(where `np` is `import numpy as np`)

Or we can simplify it:

```python
def cosine(u, v):
    u_norm = np.sqrt(u.dot(u))
    v_norm = np.sqrt(v.dot(v))
    return u.dot(v) / (u_norm * v_norm)
```

Now let's use this function to compute the
A->Q->A cosine similarity.

We will use the results from [our gpt-4o-mini evaluations](https://github.com/DataTalksClub/llm-zoomcamp/blob/main/03-evaluation/rag_evaluation/data/results-gpt4o-mini.csv):


```python
results_url = url_prefix + 'rag_evaluation/data/results-gpt4o-mini.csv'
df_results = pd.read_csv(results_url)
```


When creating embeddings, we will use a simple way -
the same we used in the [Embeddings](#embeddings) section:

```python
pipeline = make_pipeline(
    TfidfVectorizer(min_df=3),
    TruncatedSVD(n_components=128, random_state=1)
)
```

Let's fit the vectorizer on all the text data we have:

```python
pipeline.fit(df_results.answer_llm + ' ' + df_results.answer_orig + ' ' + df_results.question)
```

Now use the `transform` method of the pipeline to create the embeddings and calculate the cosine similarity between each
pair.

What's the average cosine?

- 0.64
- 0.74
- 0.84
- 0.94

This is how you do it:

- For each answer pair, compute
    - `v_llm` for the answer from the LLM 
    - `v_orig` for the original answer
    - then compute the cosine between them
- At the end, take the average


## Q6. Rouge

And alternative way to see how two texts are similar is ROUGE. 

This is a set of metrics that compares two answers based on the overlap of n-grams, word sequences, and word pairs.

It can give a more nuanced view of text similarity than just cosine similarity alone.

We don't need to implement it ourselves, there's a python package for it:

```bash
pip install rouge
```

(The latest version at the moment of writing is `1.0.1`)

Let's compute the ROUGE score between the answers at the index 10 of our dataframe (`doc_id=5170565b`)

```
from rouge import Rouge
rouge_scorer = Rouge()

r = df_results.iloc[10]
scores = rouge_scorer.get_scores(r.answer_llm, r.answer_orig)[0]
scores
```

There are three scores: `rouge-1`, `rouge-2` and `rouge-l`, and precision, recall and F1 score for each.

* `rouge-1` - the overlap of unigrams,
* `rouge-2` - bigrams,
* `rouge-l` - the longest common subsequence

For the 10th document, Rouge-1 F1 score is 0.45

Let's compute it for the pairs in the entire dataframe.
What's the average Rouge-1 F1?

- 0.25
- 0.35
- 0.45
- 0.55


## Submit the results

* Submit your results here: https://courses.datatalks.club/llm-zoomcamp-2025/homework/hw3
* It's possible that your answers won't match exactly. If it's the case, select the closest one.
