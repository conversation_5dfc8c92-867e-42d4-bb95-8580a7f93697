# Key Takeaways from the LLM Zoomcamp 2025 Launch Stream

[![Watch the Launch Stream](https://img.youtube.com/vi/FgnelhEJFj0/maxresdefault.jpg)](https://youtu.be/FgnelhEJFj0)

> **[Watch the LLM Zoomcamp 2025 Launch Stream](https://youtu.be/FgnelhEJFj0)**

---

## Table of Contents

- [Quick Summary](#quick-summary)
- [Meet the Team](#meet-the-2025-team)
- [Prerequisites](#prerequisites)
- [Course Architecture](#course-architecture)
- [Module Breakdown](#module-breakdown)
- [Timeline & Deadlines](#timeline--deadlines)
- [Getting Help](#getting-help)
- [Earning Points](#earning-points)
- [Communication Channels](#communication-channels)
- [Sponsors & Support](#sponsors--support)
- [FAQ Highlights](#faq-highlights)
- [Next Steps](#next-steps)

---

## Quick Summary

<details>
<summary><strong>Key Takeaways (Click to expand)</strong></summary>

* **Central project**: Build a production-ready RAG chatbot
* **Skills required**: Python, CLI, Git, Docker—nothing more
* **Support workflow**: FAQ → Slack search → bot → channel (no tags)
* **Earn points** by contributing to FAQ and sharing progress publicly
* **No GPUs needed** for the main camp; open-source spin-off covers that
* **Capstone & peer review** are mandatory for certificate—start gathering data now
* **Budget**: ~$10 OpenAI credit covers the entire course

</details>

## Meet the 2025 Team

| Instructor | Role | Expertise |
|------------|------|-----------|
| **[Alexey Grigorev](https://github.com/alexeygrigorev)** | Host & General Guidance | Course Creator, ML Engineering |
| **[Kacper Łukowski](https://github.com/kacperlukawski)** | Vector Search Module | Qdrant Expert |
| **Timur S** | Best Practices Module | Production ML Systems |
| **Phoenix Expert** | Monitoring Module | ML Observability (name TBA) |

## Prerequisites

### Required Skills
- **Python basics** (one-day refresher is sufficient)
- **Command-line & Git** confidence
- **Docker** installed and working

### Need a Refresher?
- **Python**: Any weekend tutorial will do
- **Docker**: Check out the [Data Engineering Zoomcamp Docker lesson](https://github.com/DataTalksClub/data-engineering-zoomcamp/tree/main/01-docker-terraform)
- **Git**: [GitHub's Git Handbook](https://guides.github.com/introduction/git-handbook/)

## Course Architecture

The entire Zoomcamp revolves around building a **production-grade RAG (Retrieval-Augmented Generation) chatbot** that answers student questions from the course FAQ.

### What You'll Build:
1. **Document Ingestion** → Text + vector indexing
2. **Smart Retrieval** → Find most relevant snippets
3. **LLM Integration** → Generate context-aware answers
4. **Production Monitoring** → Track quality, latency, and costs

## Module Breakdown

| Week | Module | Focus | 
|------|--------|-------|
| 1 | **Intro & RAG Foundations** | Core concepts, basic implementation | 
| 2 | **Vector vs Text Search** | Hybrid search strategies |
| 3 | **Evaluation Techniques** | Measuring RAG performance |
| 4 | **Monitoring with Phoenix** | Production observability |
| 5 | **Best Practices & Guardrails** | Security, reliability |
| 6-7 | **Capstone Project** | Build your own RAG system |
| 8 | **Peer Review** | Evaluate classmates' projects |
## Timeline & Deadlines

### Content Delivery
- **Pre-recorded videos** in organized playlists:
  - **[Main 2025 Playlist](https://youtube.com/playlist?list=PL3MmuxUbc_hIoBpuc900htYF4uhEAbaT-&si=uwC6I0wFePjdmLdp)**
  - **[Legacy 2024 Content](https://youtube.com/playlist?list=PL3MmuxUbc_hKiIVNf7DeEt_tGjypOYtKV&si=l7lTHbVAUHks2AMP)**

### Homework & Submissions
- **Platform**: [DataTalks.Club Course Platform](https://courses.datatalks.club/llm-zoomcamp-2025/)
- **Scoring**: Points-based system with public leaderboard
- **Deadline**: 3 weeks after each module launch

### Certificate Requirements
- Complete all homework assignments
- Build and submit capstone project (2 weeks)
- Complete peer review process (1 week)
- Minimum point threshold (TBA)

## Getting Help

### Support Workflow (Follow This Order!)

1. **Search the [FAQ Document](https://docs.google.com/document/d/1m2KexowAXTmexfC5rVTCSnaShvdUQ8Ag2IEiwBDHxN0/edit?usp=sharing)** first
2. **Search [Slack](https://datatalks.club/slack.html) history** for similar questions
3. **Ask the Slack bot** (uses the same RAG pipeline you're building!)
4. **Post in `#course-llm-zoomcamp`** (**NEVER tag instructors directly**)

### How to Ask Good Questions
- Include error messages and code snippets
- Mention what you've already tried
- Use thread replies to keep channels organized

## Earning Points

### Ways to Boost Your Score:
- **Contribute to FAQ**: Add solved issues and solutions
- **Share publicly**: Post progress on LinkedIn/Twitter with **#LLMZoomcamp**
- **Limit**: Up to 7 social media links per module count toward score
- **Quality over quantity**: Thoughtful posts get more engagement

### Content Ideas:
- Weekly progress updates
- Code snippets and explanations
- Challenges you overcame
- Creative applications of course concepts

## Communication Channels

| Channel | Purpose | Link |
|---------|---------|------|
| **Telegram** | Announcements only | [Join Channel](https://t.me/llm_zoomcamp) |
| **Slack** | Questions & peer help | [Join Workspace](https://datatalks.club/slack.html) |
| **YouTube** | Video content | [Course Playlist](https://youtube.com/playlist?list=PL3MmuxUbc_hIoBpuc900htYF4uhEAbaT-&si=uwC6I0wFePjdmLdp) |
| **GitHub** | Course materials | [Main Repository](https://github.com/DataTalksClub/llm-zoomcamp) |

### Communication Etiquette:
- Always reply in **threads** to keep channels clean
- Search before asking
- Be respectful and helpful to peers
- Don't tag instructors directly

## Sponsors & Support

### Course Sponsors
- **[Arize AI](https://github.com/Arize-ai/phoenix)**
- **[dltHub](https://github.com/dlt-hub/dlt)**
- **[Qdrant](https://github.com/qdrant/qdrant)**

*These sponsors keep the course completely free for everyone!*

### Support Alexey
If you find value in this course, consider supporting via [GitHub Sponsors](https://github.com/sponsors/alexeygrigorev).

## FAQ Highlights

<details>
<summary><strong>Why no agents in the main course?</strong></summary>

**Current situation**: Agent frameworks evolve "every day," making it risky to lock the course to unstable APIs.

**Future plans**: 
- **AI-Dev Tools Course** planned for **September 2025**
- **2,100+ sign-ups** already confirmed interest
- **Will cover**: Agentic search, LangGraph, CrewAI, agent guardrails
- **Expect**: Workshop teasers during this cohort

**More info**: [AI Dev Tools Zoomcamp Repository](https://github.com/DataTalksClub/ai-dev-tools-zoomcamp)

</details>

<details>
<summary><strong>Do I need GPUs?</strong></summary>

**Main LLM Zoomcamp**: 
- **No GPUs needed**
- **~$10 OpenAI credit** covers everything
- **Uses hosted models**: OpenAI, Anthropic, Groq, etc.

**Open-Source LLM Mini-Course** (June/July 2025):
- **Free GPU quotas** provided by Saturn Cloud & AMD
- **Local models**: Llama 3, DeepSeek, etc.
- **Topics**: Quantization, vLLM, LoRA fine-tuning

**More info**: [Open-Source LLM Zoomcamp](https://github.com/DataTalksClub/open-source-llm-zoomcamp)

</details>

<details>
<summary><strong>How is LLM monitoring different from traditional MLOps?</strong></summary>

**Shared aspects**:
- Uptime & latency tracking
- Performance regression detection
- Post-deployment monitoring

**LLM-specific metrics**:
- **Cost per call/token**
- **Hallucination detection** (via eval sets, heuristics, human feedback)
- **Prompt/response drift** (style/length changes indicating model updates)
- **Content risk**: PII leaks, toxicity detection

**Tools**: Phoenix (open-source) for dashboards and budget alerts

</details>

<details>
<summary><strong>Local vs Hosted Models: Which to choose?</strong></summary>

| Aspect | Hosted API | Local/Self-hosted |
|--------|------------|-------------------|
| **Setup** | Single HTTP call | Download 4-40GB weights |
| **Scaling** | Provider handles it | You manage infrastructure |
| **Cost** | Pay per usage | Higher upfront, lower long-term |
| **Control** | Limited customization | Full control, no data sharing |
| **Models** | Latest frontier models | Open-source alternatives |
| **Customization** | API parameters only | LoRA fine-tuning possible |

**Course approach**: Prototype with GPT-4o, then compare with local Llama 3

</details>

<details>
<summary><strong>Job market advice for LLM engineers</strong></summary>

**Show, don't tell**:
- Publish notebooks and blog posts
- Create short demo videos (Loom)
- Deploy working RAG pipelines

**Specialize wisely**:
- RAG evaluation techniques
- Retrieval optimization
- LLM cost monitoring
- Content safety & guardrails

**Network via "learning in public"**:
- Use **#LLMZoomCamp** hashtag
- Consistent LinkedIn/Twitter posts
- Engage with course community

**Portfolio > certificates**:
- Capstone GitHub repo
- Regular social media updates
- Video walkthroughs of projects

</details>

<details>
<summary><strong>Capstone project guidelines</strong></summary>

**Start early**:
- Gather domain corpus (docs, Slack dumps, PDFs)
- Begin data cleaning and chunking
- Choose a problem you're passionate about

**Requirements preview**:
- Working RAG demonstration
- Evaluation notebook with metrics
- README with cost analysis
- Monitoring screenshots
- 3-minute video walkthrough

**Peer review process**:
- 2 weeks for building
- 1 week for reviewing 3 classmates' projects
- Mandatory for certification

**Detailed rubric**: Will be published mid-cohort on the course platform

</details>

## Next Steps

1. **[Star the GitHub repo](https://github.com/DataTalksClub/llm-zoomcamp)** (helps with visibility!)
2. **Skim Module 1** content to get familiar
3. **Install Docker** and verify it works
4. **Join communication channels**:
   - [Slack workspace](https://datatalks.club/slack.html)
   - [Telegram channel](https://t.me/llm_zoomcamp)
5. **Bookmark the [FAQ document](https://docs.google.com/document/d/1m2KexowAXTmexfC5rVTCSnaShvdUQ8Ag2IEiwBDHxN0/edit?usp=sharing)**

### Week 1 Goals:
- **Watch Module 1 videos**
- **Complete first homework**
- **Start thinking about capstone dataset**
- **Share your journey** with #LLMZoomCamp

### Long-term Success:
- **Consistent engagement** with course materials
- **Active participation** in community discussions
- **Regular progress sharing** on social media
- **Early capstone planning** and data preparation

## Quick Links Reference

| Resource | Link |
|----------|------|
| **Main Repository** | https://github.com/DataTalksClub/llm-zoomcamp |
| **Course Platform** | https://courses.datatalks.club/llm-zoomcamp-2025/ |
| **2025 Playlist** | https://youtube.com/playlist?list=PL3MmuxUbc_hIoBpuc900htYF4uhEAbaT-&si=uwC6I0wFePjdmLdp |
| **FAQ Document** | https://docs.google.com/document/d/1m2KexowAXTmexfC5rVTCSnaShvdUQ8Ag2IEiwBDHxN0/edit?usp=sharing |
| **Slack Workspace** | https://datatalks.club/slack.html |
| **Telegram Channel** | https://t.me/llm_zoomcamp |
| **Project Guidelines** | https://github.com/DataTalksClub/llm-zoomcamp/blob/main/cohorts/2025/project.md |
| **Support Alexey** | https://github.com/sponsors/alexeygrigorev |
