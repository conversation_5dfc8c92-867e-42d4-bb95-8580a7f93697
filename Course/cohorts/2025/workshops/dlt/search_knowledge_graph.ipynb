{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-29T15:38:56.315510900Z", "start_time": "2025-06-29T15:38:47.147819100Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "\u001b[2m2025-06-29T15:38:48.088189\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mDeleted old log file: C:\\Users\\<USER>\\Documents\\GitHub\\venvs\\llm-zoomcamp\\lib\\site-packages\\logs\\2025-06-27_18-04-02.log\u001b[0m [\u001b[0m\u001b[1m\u001b[34mcognee.shared.logging_utils\u001b[0m]\u001b[0m\n", "\n", "\u001b[2m2025-06-29T15:38:48.088189\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mLogging initialized           \u001b[0m [\u001b[0m\u001b[1m\u001b[34mcognee.shared.logging_utils\u001b[0m]\u001b[0m \u001b[36mcognee_version\u001b[0m=\u001b[35m0.1.44\u001b[0m \u001b[36mos_info\u001b[0m=\u001b[35m'Windows 10 (10.0.22631)'\u001b[0m \u001b[36mpython_version\u001b[0m=\u001b[35m3.10.5\u001b[0m \u001b[36mstructlog_version\u001b[0m=\u001b[35m25.4.0\u001b[0m\n", "\n", "\u001b[2m2025-06-29T15:38:48.088189\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWant to learn more? Visit the Cognee documentation: https://docs.cognee.ai\u001b[0m [\u001b[0m\u001b[1m\u001b[34mcognee.shared.logging_utils\u001b[0m]\u001b[0m\n", "\n", "\u001b[1mHTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json \"HTTP/1.1 200 OK\"\u001b[0m\n"]}], "source": ["import cognee\n", "from cognee.api.v1.search import SearchType\n", "from cognee.modules.engine.models import NodeSet"]}, {"cell_type": "code", "execution_count": 18, "outputs": [], "source": ["async def search_cognee(query, node_set, query_type=SearchType.GRAPH_COMPLETION):\n", "    answer = await cognee.search(\n", "        query_text=query,\n", "        query_type=query_type,\n", "        node_type=NodeSet,\n", "        node_name=node_set\n", "    )\n", "    return answer"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T16:04:46.521295800Z", "start_time": "2025-06-29T16:04:46.505654600Z"}}, "id": "7c411d23c4d25853"}, {"cell_type": "code", "execution_count": 3, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "\u001b[1mEmbeddingRateLimiter initialized: enabled=False, requests_limit=60, interval_seconds=60\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\u001b[92m16:07:28 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\u001b[92m16:07:28 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:28 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\u001b[92m16:07:28 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:28 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:29 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\u001b[92m16:07:30 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\n", "\u001b[1mHTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\u001b[0m\u001b[92m16:07:31 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\n", "\u001b[1mLangfuse client is disabled since no public_key was provided as a parameter or environment variable 'LANGFUSE_PUBLIC_KEY'. See our docs: https://langfuse.com/docs/sdk/python/low-level-sdk#initialize-client\u001b[0m\u001b[92m16:07:33 - LiteLLM:INFO\u001b[0m: utils.py:2929 - \n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\n", "\u001b[1m\n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\u001b[0m\u001b[92m16:07:37 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m\u001b[92m16:07:37 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m"]}], "source": ["results = await search_cognee(\n", "    query=\"What API endpoints are in the Ticketmaster api? Give me specific endpoint urls.\",\n", "    node_set=['developer.ticketmaster.com']\n", ")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T14:07:37.703006100Z", "start_time": "2025-06-29T14:07:25.203355900Z"}}, "id": "5ed879e375ccfb68"}, {"cell_type": "code", "execution_count": 4, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here are some of the API endpoints in the Ticketmaster API:\n", "\n", "1. Event Search: `https://developer.ticketmaster.com/explore` - Power your event discovery experience.\n", "2. Partner API: `https://developer.ticketmaster.com/support/partner-api-faq` - Access to the Partner API for specific use cases.\n", "3. Ticket Management: `https://developer.ticketmaster.com/explore` - Validate tickets and manage scanning devices.\n", "4. Inventory Status: `https://developer.ticketmaster.com/explore` - Get status for primary Ticketmaster inventory with real-time updates.\n", "5. Shipping Options: [GET] Shipping endpoint - Fetch valid shipping options for an event.\n"]}], "source": ["print(results[0])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T14:07:37.722548200Z", "start_time": "2025-06-29T14:07:37.706911800Z"}}, "id": "a4535a0f3783e01f"}, {"cell_type": "code", "execution_count": 5, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:39 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:41 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:41 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:07:43 - LiteLLM:INFO\u001b[0m: utils.py:2929 - \n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\n", "\u001b[1m\n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\u001b[0m\u001b[92m16:07:45 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m\u001b[92m16:07:45 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m"]}], "source": ["results = await search_cognee(\n", "    query=\"What auth info do i need in the ticketmaster API?\",\n", "    node_set=['developer.ticketmaster.com']\n", ")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T14:07:45.454409Z", "start_time": "2025-06-29T14:07:37.722548200Z"}}, "id": "9fb00f7e802a0811"}, {"cell_type": "code", "execution_count": 6, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["To use the Ticketmaster API, you need authorization. Specifically, it requires API authorization to access its services.\n"]}], "source": ["print(results[0])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T14:07:45.485727700Z", "start_time": "2025-06-29T14:07:45.470074600Z"}}, "id": "7780d3730129559b"}, {"cell_type": "code", "execution_count": 7, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:21 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:22 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:22 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:24 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:24 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m16:08:25 - LiteLLM:INFO\u001b[0m: utils.py:2929 - \n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\n", "\u001b[1m\n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\u001b[0m\u001b[92m16:08:28 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m\u001b[92m16:08:28 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m"]}], "source": ["results = await search_cognee(\n", "    query=\"What pagination do i need in the ticketmaster API?\",\n", "    node_set=['developer.ticketmaster.com']\n", ")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T14:08:28.316460100Z", "start_time": "2025-06-29T14:08:19.856433100Z"}}, "id": "26eae9036e0a3619"}, {"cell_type": "code", "execution_count": 8, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["To implement pagination in the Ticketmaster API, you typically need to use parameters such as `limit` to specify the number of results per page and `offset` to determine the starting point of the results. The API documentation should provide details on how to use these parameters effectively.\n"]}], "source": ["print(results[0])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T14:08:30.419447Z", "start_time": "2025-06-29T14:08:30.397569800Z"}}, "id": "ee43c774778c6107"}, {"cell_type": "code", "execution_count": 6, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:49 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:51 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:51 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m17:39:52 - LiteLLM:INFO\u001b[0m: utils.py:2929 - \n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\n", "\u001b[1m\n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\u001b[0m\u001b[92m17:39:54 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m\u001b[92m17:39:54 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m"]}], "source": ["results = await search_cognee(\n", "    query=\"What pagination do i need in the ticketmaster API?\",\n", "    node_set=['developer.ticketmaster.com']\n", ")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T15:39:54.930438200Z", "start_time": "2025-06-29T15:39:48.198131100Z"}}, "id": "8d737a18d7af915b"}, {"cell_type": "markdown", "source": ["# Broad Search"], "metadata": {"collapsed": false}, "id": "ce04448fd04d751d"}, {"cell_type": "code", "execution_count": 28, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[92m18:07:00 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:07:00 - LiteLLM:INFO\u001b[0m: utils.py:2929 - \n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\n", "\u001b[1m\n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\u001b[0m\u001b[92m18:07:03 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m\u001b[92m18:07:03 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m"]}], "source": ["results = await search_cognee('''\n", "    What sort of API information is in this knowledge graph?\n", "    ''', \n", "    node_set=['docs'], query_type=SearchType.RAG_COMPLETION)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T16:07:03.357597200Z", "start_time": "2025-06-29T16:06:59.191739900Z"}}, "id": "eb3f612a6c036393"}, {"cell_type": "code", "execution_count": 29, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The monday.com API information includes the ability to read and update data within a monday.com account using GraphQL. It supports operations on boards, items, column values, users, and workspaces. Use cases for the API involve accessing board data for reports, creating items when records are created in other systems, and importing data. Admins, members, and guests can use the API, with varying levels of access. The API supports multiple monday products, excluding Workforms, and provides a comprehensive GraphQL schema for querying and mutations.\n"]}], "source": ["print(results[0])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T16:07:04.721481700Z", "start_time": "2025-06-29T16:07:04.688489500Z"}}, "id": "fe3995eff065dca6"}, {"cell_type": "markdown", "source": ["Using just the `docs` node set does not work - since it goes over v limited nodes "], "metadata": {"collapsed": false}, "id": "a08f7e4ac4d6ae0d"}, {"cell_type": "markdown", "source": ["# Focused Search"], "metadata": {"collapsed": false}, "id": "dbb22f5b523bf1fb"}, {"cell_type": "code", "execution_count": 36, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:38 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:40 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:40 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/text-embedding-3-large\n", "\u001b[1mselected model name for cost calculation: openai/text-embedding-3-large\u001b[0m\u001b[92m18:10:41 - LiteLLM:INFO\u001b[0m: utils.py:2929 - \n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\n", "\u001b[1m\n", "LiteLLM completion() model= gpt-4o-mini; provider = openai\u001b[0m\u001b[92m18:10:43 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m\u001b[92m18:10:44 - LiteLLM:INFO\u001b[0m: cost_calculator.py:655 - selected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\n", "\u001b[1mselected model name for cost calculation: openai/gpt-4o-mini-2024-07-18\u001b[0m"]}], "source": ["results = await search_cognee('''\n", "    What sort of API information is in this knowledge graph?\n", "    ''', \n", "    node_set=['developer.ticketmaster.com'])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T16:10:44.154220100Z", "start_time": "2025-06-29T16:10:35.865601Z"}}, "id": "79599a974c8d90ab"}, {"cell_type": "code", "execution_count": 37, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The knowledge graph contains information about various types of APIs, including 'publish api', 'presence api', 'top picks api', 'partner api', 'international discovery api', and 'discovery api'. These APIs are categorized under a general 'api' node, with relationships indicating that each specific API is a type of the general API.\n"]}], "source": ["print(results[0])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-29T16:10:44.169840500Z", "start_time": "2025-06-29T16:10:44.154220100Z"}}, "id": "e4c7c76523e2bb10"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}